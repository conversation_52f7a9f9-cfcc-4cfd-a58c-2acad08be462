import 'package:flutter_test/flutter_test.dart';
import 'package:aai/services/pdf_generation_service.dart';
import 'package:aai/models/user_model.dart';
import 'dart:io';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  group('PDFGenerationService', () {
    late AppUser testUser;
    late List<Map<String, String>> testBenefits;
    late Map<String, String> testPremiumDetails;

    setUp(() {
      testUser = AppUser(
        id: 'test-user-id',
        email: '<EMAIL>',
        displayName: '<PERSON>',
        phoneNumber: '+91 **********',
        isEmailVerified: true,
        authProvider: AuthProvider.email,
        createdAt: DateTime.now(),
      );

      testBenefits = [
        {
          'type': 'Hospitalization',
          'description': 'Room and board, ICU, surgery',
          'coverage': '100% after deductible',
          'limit': '₹5,00,000',
          'combinedDescription': 'Room and board, ICU, surgery - 100% after deductible up to ₹5,00,000',
        },
        {
          'type': 'Outpatient Care',
          'description': 'Doctor visits, consultations',
          'coverage': '80% after copay',
          'limit': '₹50,000',
          'combinedDescription': 'Doctor visits, consultations - 80% after copay up to ₹50,000',
        },
      ];

      testPremiumDetails = {
        'Annual Premium': '₹15,000',
        'Monthly Premium': '₹1,250',
        'Processing Fee': '₹500',
        'GST (18%)': '₹2,790',
        'Total Amount': '₹18,290',
      };
    });

    test('should generate PDF file successfully', () async {
      final pdfFile = await PDFGenerationService.generateProductDetailsPDF(
        customerName: 'Jane Smith',
        user: testUser,
        companyName: 'SecureHealth',
        productName: 'Health Insurance',
        planName: 'Premium Plan',
        sumInsured: '₹50,00,000',
        benefits: testBenefits,
        premiumDetails: testPremiumDetails,
      );

      expect(pdfFile, isA<File>());
      expect(await pdfFile.exists(), isTrue);
      expect(pdfFile.path.endsWith('.pdf'), isTrue);

      // Clean up
      if (await pdfFile.exists()) {
        await pdfFile.delete();
      }
    });

    test('should handle empty customer name', () async {
      final pdfFile = await PDFGenerationService.generateProductDetailsPDF(
        customerName: '',
        user: testUser,
        companyName: 'SecureHealth',
        productName: 'Health Insurance',
        planName: 'Premium Plan',
        sumInsured: '₹50,00,000',
        benefits: testBenefits,
        premiumDetails: testPremiumDetails,
      );

      expect(pdfFile, isA<File>());
      expect(await pdfFile.exists(), isTrue);

      // Clean up
      if (await pdfFile.exists()) {
        await pdfFile.delete();
      }
    });

    test('should handle different company names', () async {
      final companies = ['SecureHealth', 'LifeGuard', 'TravelSafe', 'UnknownCompany'];
      
      for (final company in companies) {
        final pdfFile = await PDFGenerationService.generateProductDetailsPDF(
          customerName: 'Test Customer',
          user: testUser,
          companyName: company,
          productName: 'Test Product',
          planName: 'Test Plan',
          sumInsured: '₹10,00,000',
          benefits: testBenefits,
          premiumDetails: testPremiumDetails,
        );

        expect(pdfFile, isA<File>());
        expect(await pdfFile.exists(), isTrue);

        // Clean up
        if (await pdfFile.exists()) {
          await pdfFile.delete();
        }
      }
    });

    test('should handle empty benefits list', () async {
      final pdfFile = await PDFGenerationService.generateProductDetailsPDF(
        customerName: 'Test Customer',
        user: testUser,
        companyName: 'SecureHealth',
        productName: 'Health Insurance',
        planName: 'Premium Plan',
        sumInsured: '₹50,00,000',
        benefits: [],
        premiumDetails: testPremiumDetails,
      );

      expect(pdfFile, isA<File>());
      expect(await pdfFile.exists(), isTrue);

      // Clean up
      if (await pdfFile.exists()) {
        await pdfFile.delete();
      }
    });

    test('should handle user with minimal information', () async {
      final minimalUser = AppUser(
        id: 'minimal-user',
        isEmailVerified: false,
        authProvider: AuthProvider.email,
        createdAt: DateTime.now(),
      );

      final pdfFile = await PDFGenerationService.generateProductDetailsPDF(
        customerName: 'Test Customer',
        user: minimalUser,
        companyName: 'SecureHealth',
        productName: 'Health Insurance',
        planName: 'Premium Plan',
        sumInsured: '₹50,00,000',
        benefits: testBenefits,
        premiumDetails: testPremiumDetails,
      );

      expect(pdfFile, isA<File>());
      expect(await pdfFile.exists(), isTrue);

      // Clean up
      if (await pdfFile.exists()) {
        await pdfFile.delete();
      }
    });
  });
}

import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../models/user_role.dart';

class SupabaseProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;
  final firebase_auth.FirebaseAuth _firebaseAuth = firebase_auth.FirebaseAuth.instance;

  /// Automatically create a Supabase profile when user signs up with Firebase Auth
  Future<ProfileCreationResult> createProfileOnSignup(firebase_auth.User firebaseUser) async {
    try {
      // Prepare user data from Firebase Auth
      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_auth_provider': _getAuthProvider(firebaseUser),
      };

      // Call Supabase function to handle signup
      final response = await _supabase.rpc('handle_user_signup', params: userData);

      if (response['success'] == true) {
        // Parse the successful response
        final profileData = response as Map<String, dynamic>;
        
        return ProfileCreationResult.success(
          profileId: profileData['profile_id'],
          firebaseUid: profileData['firebase_uid'],
          email: profileData['email'],
          displayName: profileData['display_name'],
          role: UserRole.fromJson(profileData['role']),
          message: profileData['message'],
        );
      } else {
        // Handle creation failure
        return ProfileCreationResult.failure(
          error: response['error'] ?? 'Unknown error',
          message: response['message'] ?? 'Failed to create profile',
        );
      }
    } catch (e) {
      return ProfileCreationResult.failure(
        error: e.toString(),
        message: 'Exception during profile creation: ${e.toString()}',
      );
    }
  }

  /// Sync Firebase Auth user data to Supabase profile
  Future<bool> syncFirebaseUserData(firebase_auth.User firebaseUser) async {
    try {
      final userData = {
        'p_firebase_uid': firebaseUser.uid,
        'p_email': firebaseUser.email,
        'p_display_name': firebaseUser.displayName,
        'p_phone_number': firebaseUser.phoneNumber,
        'p_photo_url': firebaseUser.photoURL,
        'p_is_email_verified': firebaseUser.emailVerified,
      };

      final result = await _supabase.rpc('sync_firebase_user_data', params: userData);
      return result == true;
    } catch (e) {
      print('Error syncing Firebase user data: $e');
      return false;
    }
  }

  /// Get profile with role information
  Future<AppUser?> getProfileWithRole(String firebaseUid) async {
    try {
      final response = await _supabase
          .rpc('get_profile_with_role', params: {'uid': firebaseUid});

      if (response != null && response.isNotEmpty) {
        return AppUser.fromSupabaseWithRole(response.first);
      }
      return null;
    } catch (e) {
      print('Error getting profile with role: $e');
      return null;
    }
  }

  /// Check if profile exists for Firebase UID
  Future<bool> profileExists(String firebaseUid) async {
    try {
      final response = await _supabase
          .from('profiles')
          .select('id')
          .eq('firebase_uid', firebaseUid)
          .is_('deleted_at', null)
          .maybeSingle();

      return response != null;
    } catch (e) {
      print('Error checking profile existence: $e');
      return false;
    }
  }

  /// Get available roles for role selection
  Future<List<UserRole>> getAvailableRoles() async {
    try {
      final response = await _supabase
          .from('roles')
          .select()
          .eq('is_active', true)
          .order('hierarchy_level');

      return response.map((role) => UserRole.fromSupabase(role)).toList();
    } catch (e) {
      print('Error getting available roles: $e');
      return [];
    }
  }

  /// Update user profile
  Future<bool> updateProfile(String firebaseUid, Map<String, dynamic> updates) async {
    try {
      await _supabase
          .from('profiles')
          .update(updates)
          .eq('firebase_uid', firebaseUid);
      return true;
    } catch (e) {
      print('Error updating profile: $e');
      return false;
    }
  }

  /// Check user permission
  Future<bool> hasPermission(String firebaseUid, String permission) async {
    try {
      final response = await _supabase.rpc('user_has_permission', params: {
        'uid': firebaseUid,
        'permission_key': permission,
      });
      return response == true;
    } catch (e) {
      print('Error checking permission: $e');
      return false;
    }
  }

  /// Helper method to determine auth provider from Firebase User
  String _getAuthProvider(firebase_auth.User user) {
    if (user.providerData.isEmpty) return 'email';
    
    final providerId = user.providerData.first.providerId;
    switch (providerId) {
      case 'google.com':
        return 'google';
      case 'apple.com':
        return 'apple';
      case 'phone':
        return 'phone';
      default:
        return 'email';
    }
  }
}

/// Result class for profile creation operations
class ProfileCreationResult {
  final bool isSuccess;
  final String? profileId;
  final String? firebaseUid;
  final String? email;
  final String? displayName;
  final UserRole? role;
  final String? error;
  final String message;

  ProfileCreationResult._({
    required this.isSuccess,
    this.profileId,
    this.firebaseUid,
    this.email,
    this.displayName,
    this.role,
    this.error,
    required this.message,
  });

  factory ProfileCreationResult.success({
    required String profileId,
    required String firebaseUid,
    String? email,
    String? displayName,
    UserRole? role,
    required String message,
  }) {
    return ProfileCreationResult._(
      isSuccess: true,
      profileId: profileId,
      firebaseUid: firebaseUid,
      email: email,
      displayName: displayName,
      role: role,
      message: message,
    );
  }

  factory ProfileCreationResult.failure({
    required String error,
    required String message,
  }) {
    return ProfileCreationResult._(
      isSuccess: false,
      error: error,
      message: message,
    );
  }
}

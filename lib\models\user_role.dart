import 'package:equatable/equatable.dart';

class UserRole extends Equatable {
  final String id;
  final String roleName;
  final String roleCode;
  final String? description;
  final Map<String, dynamic> permissions;
  final int hierarchyLevel;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserRole({
    required this.id,
    required this.roleName,
    required this.roleCode,
    this.description,
    required this.permissions,
    required this.hierarchyLevel,
    required this.isActive,
    this.createdAt,
    this.updatedAt,
  });

  /// Check if this role has a specific permission
  bool hasPermission(String permission) {
    return permissions[permission] == true;
  }

  /// Get all permissions as a list of strings
  List<String> get permissionsList {
    return permissions.entries
        .where((entry) => entry.value == true)
        .map((entry) => entry.key)
        .toList();
  }

  /// Check if this role can perform an action
  bool canCreateQuotes() => hasPermission('can_create_quotes');
  bool canViewPolicies() => hasPermission('can_view_policies');
  bool canGeneratePdfs() => hasPermission('can_generate_pdfs');
  bool canManageClients() => hasPermission('can_manage_clients');
  bool canComparePolicies() => hasPermission('can_compare_policies');
  bool canProcessClaims() => hasPermission('can_process_claims');
  bool canApproveSettlements() => hasPermission('can_approve_settlements');
  bool canManageAgents() => hasPermission('can_manage_agents');
  bool canViewReports() => hasPermission('can_view_reports');
  bool canAssessRisks() => hasPermission('can_assess_risks');
  bool canApprovePolicies() => hasPermission('can_approve_policies');
  bool canSetPremiums() => hasPermission('can_set_premiums');
  bool canUpdateCustomerInfo() => hasPermission('can_update_customer_info');

  /// Check if this role is senior to another role
  bool isSeniorTo(UserRole otherRole) {
    return hierarchyLevel > otherRole.hierarchyLevel;
  }

  /// Check if this role is junior to another role
  bool isJuniorTo(UserRole otherRole) {
    return hierarchyLevel < otherRole.hierarchyLevel;
  }

  /// Get role display name with hierarchy indicator
  String get displayNameWithLevel {
    return '$roleName (Level $hierarchyLevel)';
  }

  /// Factory constructor from Supabase JSON
  factory UserRole.fromSupabase(Map<String, dynamic> json) {
    return UserRole(
      id: json['id'] as String,
      roleName: json['role_name'] as String,
      roleCode: json['role_code'] as String,
      description: json['description'] as String?,
      permissions: Map<String, dynamic>.from(json['permissions'] ?? {}),
      hierarchyLevel: json['hierarchy_level'] as int,
      isActive: json['is_active'] as bool,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  /// Factory constructor from JSON (for API responses)
  factory UserRole.fromJson(Map<String, dynamic> json) {
    return UserRole(
      id: json['id'] as String? ?? '',
      roleName: json['name'] as String? ?? json['role_name'] as String? ?? '',
      roleCode: json['code'] as String? ?? json['role_code'] as String? ?? '',
      description: json['description'] as String?,
      permissions: Map<String, dynamic>.from(json['permissions'] ?? {}),
      hierarchyLevel: json['hierarchy_level'] as int? ?? 1,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'] as String)
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  /// Convert to JSON for API calls
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'role_name': roleName,
      'role_code': roleCode,
      'description': description,
      'permissions': permissions,
      'hierarchy_level': hierarchyLevel,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Convert to Supabase format
  Map<String, dynamic> toSupabase() {
    return {
      'id': id,
      'role_name': roleName,
      'role_code': roleCode,
      'description': description,
      'permissions': permissions,
      'hierarchy_level': hierarchyLevel,
      'is_active': isActive,
    };
  }

  /// Create a copy with updated fields
  UserRole copyWith({
    String? id,
    String? roleName,
    String? roleCode,
    String? description,
    Map<String, dynamic>? permissions,
    int? hierarchyLevel,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserRole(
      id: id ?? this.id,
      roleName: roleName ?? this.roleName,
      roleCode: roleCode ?? this.roleCode,
      description: description ?? this.description,
      permissions: permissions ?? this.permissions,
      hierarchyLevel: hierarchyLevel ?? this.hierarchyLevel,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        roleName,
        roleCode,
        description,
        permissions,
        hierarchyLevel,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'UserRole(id: $id, roleName: $roleName, roleCode: $roleCode, '
           'hierarchyLevel: $hierarchyLevel, isActive: $isActive)';
  }
}

/// Predefined role codes for easy reference
class RoleCodes {
  static const String agent = 'AGENT';
  static const String broker = 'BROKER';
  static const String claimsManager = 'CLAIMS_MGR';
  static const String regionalManager = 'REGIONAL_MGR';
  static const String underwriter = 'UNDERWRITER';
  static const String customerServiceRep = 'CSR';
}

/// Extension for role-based UI features
extension UserRoleUI on UserRole {
  /// Get role color for UI display
  String get roleColor {
    switch (roleCode) {
      case RoleCodes.agent:
        return '#086788';
      case RoleCodes.broker:
        return '#157A6E';
      case RoleCodes.claimsManager:
        return '#306B34';
      case RoleCodes.regionalManager:
        return '#E92933';
      case RoleCodes.underwriter:
        return '#656565';
      case RoleCodes.customerServiceRep:
        return '#34344A';
      default:
        return '#086788';
    }
  }

  /// Get role icon for UI display
  String get roleIcon {
    switch (roleCode) {
      case RoleCodes.agent:
        return '👤';
      case RoleCodes.broker:
        return '🤝';
      case RoleCodes.claimsManager:
        return '📋';
      case RoleCodes.regionalManager:
        return '👔';
      case RoleCodes.underwriter:
        return '📊';
      case RoleCodes.customerServiceRep:
        return '🎧';
      default:
        return '👤';
    }
  }
}

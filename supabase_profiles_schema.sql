-- =====================================================
-- SUPABASE PROFILES TABLE SCHEMA (ENHANCED)
-- All About Insurance App
-- =====================================================

-- =====================================================
-- ROLES TABLE FOR NORMALIZED ROLE MANAGEMENT
-- =====================================================

-- Create roles table for scalable role management
CREATE TABLE IF NOT EXISTS public.roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_name TEXT UNIQUE NOT NULL,
    role_code TEXT UNIQUE NOT NULL, -- For programmatic reference (e.g., 'AGENT', 'BROKER')
    description TEXT,
    permissions JSONB DEFAULT '{}', -- Store role-specific permissions as JSON
    hierarchy_level INTEGER DEFAULT 1, -- For organizational hierarchy (1=lowest, higher=more senior)
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert seed data for common insurance roles
INSERT INTO public.roles (role_name, role_code, description, hierarchy_level, permissions) VALUES
('Insurance Agent', 'AGENT', 'Licensed insurance sales representative', 1, '{"can_create_quotes": true, "can_view_policies": true, "can_generate_pdfs": true}'),
('Insurance Broker', 'BROKER', 'Independent insurance intermediary', 2, '{"can_create_quotes": true, "can_manage_clients": true, "can_generate_pdfs": true, "can_compare_policies": true}'),
('Claims Manager', 'CLAIMS_MGR', 'Handles insurance claim processing', 3, '{"can_process_claims": true, "can_approve_settlements": true, "can_view_all_policies": true}'),
('Regional Manager', 'REGIONAL_MGR', 'Manages regional operations', 4, '{"can_manage_agents": true, "can_view_reports": true, "can_approve_large_claims": true}'),
('Underwriter', 'UNDERWRITER', 'Assesses insurance risks and determines coverage', 3, '{"can_assess_risks": true, "can_approve_policies": true, "can_set_premiums": true}'),
('Customer Service Representative', 'CSR', 'Handles customer inquiries and support', 1, '{"can_view_policies": true, "can_update_customer_info": true, "can_generate_pdfs": true}')
ON CONFLICT (role_code) DO NOTHING;

-- =====================================================
-- ENHANCED PROFILES TABLE WITH VALIDATION
-- =====================================================

-- Create profiles table to store comprehensive user profile data
CREATE TABLE IF NOT EXISTS public.profiles (
    -- Primary identification (using Firebase Auth UID)
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    firebase_uid TEXT UNIQUE NOT NULL, -- Firebase Auth UID as unique identifier
    
    -- Basic Authentication Info (synced from Firebase)
    email TEXT,
    is_email_verified BOOLEAN DEFAULT FALSE,
    auth_provider TEXT CHECK (auth_provider IN ('email', 'google', 'phone', 'apple')) DEFAULT 'email',
    
    -- Personal Information
    display_name TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    
    -- Profile Images (URLs to cloud storage)
    photo_url TEXT, -- Profile picture URL
    company_logo_url TEXT, -- Company logo URL
    
    -- Professional Information
    company_name TEXT,
    designation TEXT,
    role_id UUID REFERENCES public.roles(id), -- Foreign key to roles table
    gstin TEXT, -- GST Identification Number (validated format)
    
    -- Location & Language
    state_of_residence TEXT,
    native_language TEXT DEFAULT 'Hindi',
    
    -- App Preferences & Settings
    push_notifications_enabled BOOLEAN DEFAULT TRUE,
    email_notifications_enabled BOOLEAN DEFAULT FALSE,
    location_services_enabled BOOLEAN DEFAULT TRUE,
    analytics_enabled BOOLEAN DEFAULT FALSE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    biometric_enabled BOOLEAN DEFAULT TRUE,
    auto_download_enabled BOOLEAN DEFAULT FALSE,
    
    -- Profile Completion Preferences
    profile_completion_dont_ask_again BOOLEAN DEFAULT FALSE,
    
    -- Subscription Information
    subscription_plan TEXT CHECK (subscription_plan IN ('basic', 'pro', 'enterprise')) DEFAULT 'basic',
    subscription_status TEXT CHECK (subscription_status IN ('active', 'inactive', 'cancelled', 'expired')) DEFAULT 'inactive',
    subscription_start_date TIMESTAMPTZ,
    subscription_end_date TIMESTAMPTZ,
    billing_period TEXT CHECK (billing_period IN ('monthly', 'annual')) DEFAULT 'monthly',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_sign_in_at TIMESTAMPTZ,
    
    -- Soft delete support
    deleted_at TIMESTAMPTZ DEFAULT NULL,

    -- Data validation constraints
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_phone CHECK (phone_number IS NULL OR phone_number ~* '^\+?[1-9]\d{1,14}$'),
    CONSTRAINT valid_gstin CHECK (gstin IS NULL OR gstin ~* '^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$'),
    CONSTRAINT valid_birth_date CHECK (date_of_birth IS NULL OR date_of_birth <= CURRENT_DATE),
    CONSTRAINT valid_state CHECK (state_of_residence IS NULL OR LENGTH(state_of_residence) >= 2),
    CONSTRAINT valid_subscription_dates CHECK (subscription_start_date IS NULL OR subscription_end_date IS NULL OR subscription_start_date <= subscription_end_date)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Roles table indexes
CREATE INDEX IF NOT EXISTS idx_roles_code ON public.roles(role_code);
CREATE INDEX IF NOT EXISTS idx_roles_active ON public.roles(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_roles_hierarchy ON public.roles(hierarchy_level);

-- Profiles table indexes
-- Primary lookup index on firebase_uid (most common query)
CREATE INDEX IF NOT EXISTS idx_profiles_firebase_uid ON public.profiles(firebase_uid);

-- Email lookup index
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);

-- Role-based queries
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role_id);

-- Subscription queries
CREATE INDEX IF NOT EXISTS idx_profiles_subscription ON public.profiles(subscription_plan, subscription_status);

-- Soft delete support
CREATE INDEX IF NOT EXISTS idx_profiles_active ON public.profiles(firebase_uid) WHERE deleted_at IS NULL;

-- Timestamp queries
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON public.profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_profiles_updated_at ON public.profiles(updated_at);

-- Company and location queries
CREATE INDEX IF NOT EXISTS idx_profiles_company ON public.profiles(company_name);
CREATE INDEX IF NOT EXISTS idx_profiles_state ON public.profiles(state_of_residence);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on both tables
ALTER TABLE public.roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Roles table policies (read-only for authenticated users)
CREATE POLICY "Authenticated users can view roles" ON public.roles
    FOR SELECT TO authenticated USING (is_active = TRUE);

-- Admin users can manage roles (placeholder for future admin functionality)
CREATE POLICY "Admins can manage roles" ON public.roles
    FOR ALL TO authenticated USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE firebase_uid = auth.uid()::text
            AND role_id IN (
                SELECT id FROM public.roles
                WHERE role_code IN ('REGIONAL_MGR', 'ADMIN')
                AND is_active = TRUE
            )
        )
    );

-- Policy: Users can only view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- Policy: Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- Policy: Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    ) WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- Policy: Users can soft delete their own profile
CREATE POLICY "Users can delete own profile" ON public.profiles
    FOR UPDATE USING (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    ) WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to automatically update updated_at on changes
CREATE TRIGGER trigger_roles_updated_at
    BEFORE UPDATE ON public.roles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get profile with role information by Firebase UID
CREATE OR REPLACE FUNCTION public.get_profile_with_role(uid TEXT)
RETURNS TABLE (
    profile_id UUID,
    firebase_uid TEXT,
    email TEXT,
    display_name TEXT,
    phone_number TEXT,
    company_name TEXT,
    designation TEXT,
    role_name TEXT,
    role_code TEXT,
    role_permissions JSONB,
    hierarchy_level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.firebase_uid,
        p.email,
        p.display_name,
        p.phone_number,
        p.company_name,
        p.designation,
        r.role_name,
        r.role_code,
        r.permissions,
        r.hierarchy_level
    FROM public.profiles p
    LEFT JOIN public.roles r ON p.role_id = r.id
    WHERE p.firebase_uid = uid AND p.deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get profile by Firebase UID (backward compatibility)
CREATE OR REPLACE FUNCTION public.get_profile_by_firebase_uid(uid TEXT)
RETURNS SETOF public.profiles AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM public.profiles
    WHERE firebase_uid = uid AND deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get users by role
CREATE OR REPLACE FUNCTION public.get_users_by_role(role_code_param TEXT)
RETURNS SETOF public.profiles AS $$
BEGIN
    RETURN QUERY
    SELECT p.* FROM public.profiles p
    JOIN public.roles r ON p.role_id = r.id
    WHERE r.role_code = role_code_param
    AND r.is_active = TRUE
    AND p.deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check user permissions
CREATE OR REPLACE FUNCTION public.user_has_permission(uid TEXT, permission_key TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := FALSE;
BEGIN
    SELECT COALESCE((r.permissions ->> permission_key)::BOOLEAN, FALSE)
    INTO has_permission
    FROM public.profiles p
    JOIN public.roles r ON p.role_id = r.id
    WHERE p.firebase_uid = uid
    AND p.deleted_at IS NULL
    AND r.is_active = TRUE;

    RETURN COALESCE(has_permission, FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to soft delete profile
CREATE OR REPLACE FUNCTION public.soft_delete_profile(uid TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.profiles
    SET deleted_at = NOW()
    WHERE firebase_uid = uid AND deleted_at IS NULL;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate and clean phone number
CREATE OR REPLACE FUNCTION public.clean_phone_number(phone TEXT)
RETURNS TEXT AS $$
BEGIN
    -- Remove all non-digit characters except +
    phone := regexp_replace(phone, '[^\d+]', '', 'g');

    -- Add +91 prefix for Indian numbers if not present
    IF phone ~ '^\d{10}$' THEN
        phone := '+91' || phone;
    END IF;

    -- Validate final format
    IF phone ~ '^\+?[1-9]\d{1,14}$' THEN
        RETURN phone;
    ELSE
        RETURN NULL;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIAL DATA SETUP AND PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.roles TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Grant permissions for sequences (if any)
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

-- Roles table comments
COMMENT ON TABLE public.roles IS 'Normalized roles table for scalable role management';
COMMENT ON COLUMN public.roles.role_code IS 'Unique programmatic identifier for roles (e.g., AGENT, BROKER)';
COMMENT ON COLUMN public.roles.permissions IS 'JSON object containing role-specific permissions';
COMMENT ON COLUMN public.roles.hierarchy_level IS 'Organizational hierarchy level (1=lowest, higher=more senior)';

-- Profiles table comments
COMMENT ON TABLE public.profiles IS 'Comprehensive user profiles table for All About Insurance app';
COMMENT ON COLUMN public.profiles.firebase_uid IS 'Firebase Authentication UID - primary user identifier';
COMMENT ON COLUMN public.profiles.role_id IS 'Foreign key reference to roles table for normalized role management';
COMMENT ON COLUMN public.profiles.profile_completion_dont_ask_again IS 'User preference to skip profile completion prompts';
COMMENT ON COLUMN public.profiles.subscription_plan IS 'Current subscription tier: basic, pro, or enterprise';
COMMENT ON COLUMN public.profiles.deleted_at IS 'Soft delete timestamp - NULL means active profile';
COMMENT ON COLUMN public.profiles.gstin IS 'GST Identification Number - validated Indian GST format';

-- =====================================================
-- MIGRATION HELPER FUNCTIONS
-- =====================================================

-- Function to migrate existing role data to normalized structure
CREATE OR REPLACE FUNCTION public.migrate_role_data()
RETURNS TEXT AS $$
DECLARE
    migration_count INTEGER := 0;
    role_record RECORD;
BEGIN
    -- Update profiles with role_id based on existing role text
    FOR role_record IN
        SELECT DISTINCT role, COUNT(*) as user_count
        FROM public.profiles
        WHERE role IS NOT NULL AND role_id IS NULL
        GROUP BY role
    LOOP
        -- Try to match existing role text to role_name or create new role
        UPDATE public.profiles
        SET role_id = (
            SELECT id FROM public.roles
            WHERE role_name ILIKE role_record.role
            OR role_code = UPPER(REPLACE(role_record.role, ' ', '_'))
            LIMIT 1
        )
        WHERE role = role_record.role AND role_id IS NULL;

        GET DIAGNOSTICS migration_count = ROW_COUNT;
    END LOOP;

    RETURN format('Migrated %s user role assignments', migration_count);
END;
$$ LANGUAGE plpgsql;

-- Function to validate existing data before applying constraints
CREATE OR REPLACE FUNCTION public.validate_existing_data()
RETURNS TABLE (
    validation_type TEXT,
    invalid_count INTEGER,
    sample_invalid_values TEXT[]
) AS $$
BEGIN
    -- Check invalid emails
    RETURN QUERY
    SELECT
        'Invalid Emails'::TEXT,
        COUNT(*)::INTEGER,
        ARRAY_AGG(email ORDER BY email LIMIT 5)
    FROM public.profiles
    WHERE email IS NOT NULL
    AND email !~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';

    -- Check invalid phone numbers
    RETURN QUERY
    SELECT
        'Invalid Phone Numbers'::TEXT,
        COUNT(*)::INTEGER,
        ARRAY_AGG(phone_number ORDER BY phone_number LIMIT 5)
    FROM public.profiles
    WHERE phone_number IS NOT NULL
    AND phone_number !~* '^\+?[1-9]\d{1,14}$';

    -- Check invalid GSTIN
    RETURN QUERY
    SELECT
        'Invalid GSTIN'::TEXT,
        COUNT(*)::INTEGER,
        ARRAY_AGG(gstin ORDER BY gstin LIMIT 5)
    FROM public.profiles
    WHERE gstin IS NOT NULL
    AND gstin !~* '^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$';

    -- Check future birth dates
    RETURN QUERY
    SELECT
        'Future Birth Dates'::TEXT,
        COUNT(*)::INTEGER,
        ARRAY_AGG(date_of_birth::TEXT ORDER BY date_of_birth LIMIT 5)
    FROM public.profiles
    WHERE date_of_birth IS NOT NULL
    AND date_of_birth > CURRENT_DATE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- SUPABASE PROFILES TABLE SCHEMA
-- All About Insurance App
-- =====================================================

-- Create profiles table to store comprehensive user profile data
CREATE TABLE IF NOT EXISTS public.profiles (
    -- Primary identification (using Firebase Auth UID)
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    firebase_uid TEXT UNIQUE NOT NULL, -- Firebase Auth UID as unique identifier
    
    -- Basic Authentication Info (synced from Firebase)
    email TEXT,
    is_email_verified BOOLEAN DEFAULT FALSE,
    auth_provider TEXT CHECK (auth_provider IN ('email', 'google', 'phone', 'apple')) DEFAULT 'email',
    
    -- Personal Information
    display_name TEXT,
    phone_number TEXT,
    date_of_birth DATE,
    
    -- Profile Images (URLs to cloud storage)
    photo_url TEXT, -- Profile picture URL
    company_logo_url TEXT, -- Company logo URL
    
    -- Professional Information
    company_name TEXT,
    designation TEXT,
    role TEXT, -- e.g., 'Insurance Agent', 'Broker', etc.
    gstin TEXT, -- GST Identification Number
    
    -- Location & Language
    state_of_residence TEXT,
    native_language TEXT DEFAULT 'Hindi',
    
    -- App Preferences & Settings
    push_notifications_enabled BOOLEAN DEFAULT TRUE,
    email_notifications_enabled BOOLEAN DEFAULT FALSE,
    location_services_enabled BOOLEAN DEFAULT TRUE,
    analytics_enabled BOOLEAN DEFAULT FALSE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    biometric_enabled BOOLEAN DEFAULT TRUE,
    auto_download_enabled BOOLEAN DEFAULT FALSE,
    
    -- Profile Completion Preferences
    profile_completion_dont_ask_again BOOLEAN DEFAULT FALSE,
    
    -- Subscription Information
    subscription_plan TEXT CHECK (subscription_plan IN ('basic', 'pro', 'enterprise')) DEFAULT 'basic',
    subscription_status TEXT CHECK (subscription_status IN ('active', 'inactive', 'cancelled', 'expired')) DEFAULT 'inactive',
    subscription_start_date TIMESTAMPTZ,
    subscription_end_date TIMESTAMPTZ,
    billing_period TEXT CHECK (billing_period IN ('monthly', 'annual')) DEFAULT 'monthly',
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    last_sign_in_at TIMESTAMPTZ,
    
    -- Soft delete support
    deleted_at TIMESTAMPTZ DEFAULT NULL
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Primary lookup index on firebase_uid (most common query)
CREATE INDEX IF NOT EXISTS idx_profiles_firebase_uid ON public.profiles(firebase_uid);

-- Email lookup index
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);

-- Subscription queries
CREATE INDEX IF NOT EXISTS idx_profiles_subscription ON public.profiles(subscription_plan, subscription_status);

-- Soft delete support
CREATE INDEX IF NOT EXISTS idx_profiles_active ON public.profiles(firebase_uid) WHERE deleted_at IS NULL;

-- Timestamp queries
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON public.profiles(created_at);
CREATE INDEX IF NOT EXISTS idx_profiles_updated_at ON public.profiles(updated_at);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on the profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only view their own profile
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- Policy: Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- Policy: Users can update their own profile
CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    ) WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- Policy: Users can soft delete their own profile
CREATE POLICY "Users can delete own profile" ON public.profiles
    FOR UPDATE USING (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    ) WITH CHECK (
        firebase_uid = auth.jwt() ->> 'sub' OR
        firebase_uid = auth.uid()::text
    );

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on profile changes
CREATE TRIGGER trigger_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_updated_at();

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to get profile by Firebase UID
CREATE OR REPLACE FUNCTION public.get_profile_by_firebase_uid(uid TEXT)
RETURNS SETOF public.profiles AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM public.profiles
    WHERE firebase_uid = uid AND deleted_at IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to soft delete profile
CREATE OR REPLACE FUNCTION public.soft_delete_profile(uid TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.profiles
    SET deleted_at = NOW()
    WHERE firebase_uid = uid AND deleted_at IS NULL;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- INITIAL DATA SETUP (OPTIONAL)
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON TABLE public.profiles IS 'Comprehensive user profiles table for All About Insurance app';
COMMENT ON COLUMN public.profiles.firebase_uid IS 'Firebase Authentication UID - primary user identifier';
COMMENT ON COLUMN public.profiles.profile_completion_dont_ask_again IS 'User preference to skip profile completion prompts';
COMMENT ON COLUMN public.profiles.subscription_plan IS 'Current subscription tier: basic, pro, or enterprise';
COMMENT ON COLUMN public.profiles.deleted_at IS 'Soft delete timestamp - NULL means active profile';

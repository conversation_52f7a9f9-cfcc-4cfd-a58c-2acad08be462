import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'user_role.dart';

enum AuthProvider {
  email,
  google,
  phone,
  apple,
}

class AppUser extends Equatable {
  final String id;
  final String? email;
  final String? displayName;
  final String? photoURL;
  final String? phoneNumber;
  final bool isEmailVerified;
  final AuthProvider authProvider;
  final DateTime createdAt;
  final DateTime? lastSignInAt;

  // Enhanced Supabase profile fields
  final String? firstName;
  final String? lastName;
  final String? dateOfBirth;
  final String? gender;
  final String? address;
  final String? city;
  final String? state;
  final String? pincode;
  final String? gstinNumber;
  final String? panNumber;
  final String? aadhaarNumber;
  final String? companyName;
  final String? designation;
  final UserRole? role;
  final Map<String, dynamic>? appPreferences;
  final String? subscriptionPlan;
  final String? subscriptionStatus;
  final DateTime? subscriptionExpiresAt;
  final bool isActive;
  final DateTime? deletedAt;

  const AppUser({
    required this.id,
    this.email,
    this.displayName,
    this.photoURL,
    this.phoneNumber,
    required this.isEmailVerified,
    required this.authProvider,
    required this.createdAt,
    this.lastSignInAt,
    this.firstName,
    this.lastName,
    this.dateOfBirth,
    this.gender,
    this.address,
    this.city,
    this.state,
    this.pincode,
    this.gstinNumber,
    this.panNumber,
    this.aadhaarNumber,
    this.companyName,
    this.designation,
    this.role,
    this.appPreferences,
    this.subscriptionPlan,
    this.subscriptionStatus,
    this.subscriptionExpiresAt,
    this.isActive = true,
    this.deletedAt,
  });

  factory AppUser.fromFirebaseUser(User user) {
    AuthProvider provider = AuthProvider.email;

    // Determine auth provider
    if (user.providerData.isNotEmpty) {
      final providerId = user.providerData.first.providerId;
      switch (providerId) {
        case 'google.com':
          provider = AuthProvider.google;
          break;
        case 'phone':
          provider = AuthProvider.phone;
          break;
        case 'apple.com':
          provider = AuthProvider.apple;
          break;
        default:
          provider = AuthProvider.email;
      }
    }

    return AppUser(
      id: user.uid,
      email: user.email,
      displayName: user.displayName,
      photoURL: user.photoURL,
      phoneNumber: user.phoneNumber,
      isEmailVerified: user.emailVerified,
      authProvider: provider,
      createdAt: user.metadata.creationTime ?? DateTime.now(),
      lastSignInAt: user.metadata.lastSignInTime,
    );
  }

  /// Factory constructor from Supabase profile data with role information
  factory AppUser.fromSupabaseWithRole(Map<String, dynamic> profileData, UserRole? role) {
    // Parse auth provider
    AuthProvider provider = AuthProvider.email;
    final authProviderStr = profileData['auth_provider'] as String?;
    if (authProviderStr != null) {
      switch (authProviderStr) {
        case 'google':
          provider = AuthProvider.google;
          break;
        case 'phone':
          provider = AuthProvider.phone;
          break;
        case 'apple':
          provider = AuthProvider.apple;
          break;
        default:
          provider = AuthProvider.email;
      }
    }

    return AppUser(
      id: profileData['firebase_uid'] as String,
      email: profileData['email'] as String?,
      displayName: profileData['display_name'] as String?,
      photoURL: profileData['photo_url'] as String?,
      phoneNumber: profileData['phone_number'] as String?,
      isEmailVerified: profileData['is_email_verified'] as bool? ?? false,
      authProvider: provider,
      createdAt: DateTime.parse(profileData['created_at'] as String),
      lastSignInAt: profileData['last_sign_in_at'] != null
          ? DateTime.parse(profileData['last_sign_in_at'] as String)
          : null,
      firstName: profileData['first_name'] as String?,
      lastName: profileData['last_name'] as String?,
      dateOfBirth: profileData['date_of_birth'] as String?,
      gender: profileData['gender'] as String?,
      address: profileData['address'] as String?,
      city: profileData['city'] as String?,
      state: profileData['state'] as String?,
      pincode: profileData['pincode'] as String?,
      gstinNumber: profileData['gstin_number'] as String?,
      panNumber: profileData['pan_number'] as String?,
      aadhaarNumber: profileData['aadhaar_number'] as String?,
      companyName: profileData['company_name'] as String?,
      designation: profileData['designation'] as String?,
      role: role,
      appPreferences: profileData['app_preferences'] as Map<String, dynamic>?,
      subscriptionPlan: profileData['subscription_plan'] as String?,
      subscriptionStatus: profileData['subscription_status'] as String?,
      subscriptionExpiresAt: profileData['subscription_expires_at'] != null
          ? DateTime.parse(profileData['subscription_expires_at'] as String)
          : null,
      isActive: profileData['is_active'] as bool? ?? true,
      deletedAt: profileData['deleted_at'] != null
          ? DateTime.parse(profileData['deleted_at'] as String)
          : null,
    );
  }

  AppUser copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoURL,
    String? phoneNumber,
    bool? isEmailVerified,
    AuthProvider? authProvider,
    DateTime? createdAt,
    DateTime? lastSignInAt,
    String? firstName,
    String? lastName,
    String? dateOfBirth,
    String? gender,
    String? address,
    String? city,
    String? state,
    String? pincode,
    String? gstinNumber,
    String? panNumber,
    String? aadhaarNumber,
    String? companyName,
    String? designation,
    UserRole? role,
    Map<String, dynamic>? appPreferences,
    String? subscriptionPlan,
    String? subscriptionStatus,
    DateTime? subscriptionExpiresAt,
    bool? isActive,
    DateTime? deletedAt,
  }) {
    return AppUser(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      authProvider: authProvider ?? this.authProvider,
      createdAt: createdAt ?? this.createdAt,
      lastSignInAt: lastSignInAt ?? this.lastSignInAt,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      gstinNumber: gstinNumber ?? this.gstinNumber,
      panNumber: panNumber ?? this.panNumber,
      aadhaarNumber: aadhaarNumber ?? this.aadhaarNumber,
      companyName: companyName ?? this.companyName,
      designation: designation ?? this.designation,
      role: role ?? this.role,
      appPreferences: appPreferences ?? this.appPreferences,
      subscriptionPlan: subscriptionPlan ?? this.subscriptionPlan,
      subscriptionStatus: subscriptionStatus ?? this.subscriptionStatus,
      subscriptionExpiresAt: subscriptionExpiresAt ?? this.subscriptionExpiresAt,
      isActive: isActive ?? this.isActive,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'phoneNumber': phoneNumber,
      'isEmailVerified': isEmailVerified,
      'authProvider': authProvider.name,
      'createdAt': createdAt.toIso8601String(),
      'lastSignInAt': lastSignInAt?.toIso8601String(),
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth,
      'gender': gender,
      'address': address,
      'city': city,
      'state': state,
      'pincode': pincode,
      'gstinNumber': gstinNumber,
      'panNumber': panNumber,
      'aadhaarNumber': aadhaarNumber,
      'companyName': companyName,
      'designation': designation,
      'role': role?.toJson(),
      'appPreferences': appPreferences,
      'subscriptionPlan': subscriptionPlan,
      'subscriptionStatus': subscriptionStatus,
      'subscriptionExpiresAt': subscriptionExpiresAt?.toIso8601String(),
      'isActive': isActive,
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  factory AppUser.fromJson(Map<String, dynamic> json) {
    return AppUser(
      id: json['id'],
      email: json['email'],
      displayName: json['displayName'],
      photoURL: json['photoURL'],
      phoneNumber: json['phoneNumber'],
      isEmailVerified: json['isEmailVerified'] ?? false,
      authProvider: AuthProvider.values.firstWhere(
        (e) => e.name == json['authProvider'],
        orElse: () => AuthProvider.email,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      lastSignInAt: json['lastSignInAt'] != null
          ? DateTime.parse(json['lastSignInAt'])
          : null,
      firstName: json['firstName'],
      lastName: json['lastName'],
      dateOfBirth: json['dateOfBirth'],
      gender: json['gender'],
      address: json['address'],
      city: json['city'],
      state: json['state'],
      pincode: json['pincode'],
      gstinNumber: json['gstinNumber'],
      panNumber: json['panNumber'],
      aadhaarNumber: json['aadhaarNumber'],
      companyName: json['companyName'],
      designation: json['designation'],
      role: json['role'] != null ? UserRole.fromJson(json['role']) : null,
      appPreferences: json['appPreferences'] as Map<String, dynamic>?,
      subscriptionPlan: json['subscriptionPlan'],
      subscriptionStatus: json['subscriptionStatus'],
      subscriptionExpiresAt: json['subscriptionExpiresAt'] != null
          ? DateTime.parse(json['subscriptionExpiresAt'])
          : null,
      isActive: json['isActive'] ?? true,
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'])
          : null,
    );
  }

  @override
  List<Object?> get props => [
        id,
        email,
        displayName,
        photoURL,
        phoneNumber,
        isEmailVerified,
        authProvider,
        createdAt,
        lastSignInAt,
        firstName,
        lastName,
        dateOfBirth,
        gender,
        address,
        city,
        state,
        pincode,
        gstinNumber,
        panNumber,
        aadhaarNumber,
        companyName,
        designation,
        role,
        appPreferences,
        subscriptionPlan,
        subscriptionStatus,
        subscriptionExpiresAt,
        isActive,
        deletedAt,
      ];

  /// Get full name combining first and last name
  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName'.trim();
    } else if (displayName != null) {
      return displayName!;
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    }
    return email?.split('@').first ?? 'User';
  }

  /// Check if user has a specific permission through their role
  bool hasPermission(String permission) {
    return role?.hasPermission(permission) ?? false;
  }

  /// Get user's role name
  String get roleName => role?.roleName ?? 'No Role';

  /// Get user's role code
  String get roleCode => role?.roleCode ?? '';

  /// Check if user profile is complete
  bool get isProfileComplete {
    return firstName != null &&
           lastName != null &&
           phoneNumber != null &&
           city != null &&
           state != null;
  }

  /// Check if user has active subscription
  bool get hasActiveSubscription {
    return subscriptionStatus == 'active' &&
           (subscriptionExpiresAt == null || subscriptionExpiresAt!.isAfter(DateTime.now()));
  }

  /// Get app preference value
  T? getAppPreference<T>(String key, [T? defaultValue]) {
    if (appPreferences == null) return defaultValue;
    return appPreferences![key] as T? ?? defaultValue;
  }

  /// Check if specific app preference is enabled
  bool isAppPreferenceEnabled(String key) {
    return getAppPreference<bool>(key, false) ?? false;
  }

  /// Get user initials for profile picture fallback
  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0].toUpperCase()}${lastName![0].toUpperCase()}';
    } else if (displayName != null && displayName!.isNotEmpty) {
      final parts = displayName!.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0].toUpperCase()}${parts[1][0].toUpperCase()}';
      } else {
        return parts[0][0].toUpperCase();
      }
    } else if (email != null) {
      return email![0].toUpperCase();
    }
    return 'U';
  }

  /// Check if user is deleted (soft delete)
  bool get isDeleted => deletedAt != null;

  /// Get subscription status display text
  String get subscriptionStatusDisplay {
    switch (subscriptionStatus?.toLowerCase()) {
      case 'active':
        return 'Active';
      case 'expired':
        return 'Expired';
      case 'cancelled':
        return 'Cancelled';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  }

  /// Get days until subscription expires
  int? get daysUntilSubscriptionExpires {
    if (subscriptionExpiresAt == null) return null;
    final now = DateTime.now();
    if (subscriptionExpiresAt!.isBefore(now)) return 0;
    return subscriptionExpiresAt!.difference(now).inDays;
  }
}

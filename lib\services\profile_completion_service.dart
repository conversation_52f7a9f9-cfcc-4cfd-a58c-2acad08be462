import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class ProfileCompletionService {
  static const String _dontAskAgainKeyPrefix = 'profile_completion_dont_ask_again_';

  static ProfileCompletionService? _instance;
  static ProfileCompletionService get instance => _instance ??= ProfileCompletionService._();

  ProfileCompletionService._();

  /// Generate user-specific preference key
  String _getUserPreferenceKey(String userId) {
    return '$_dontAskAgainKeyPrefix$userId';
  }

  /// Check if user has opted to not be asked about profile completion
  Future<bool> shouldSkipProfileCompletion(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userKey = _getUserPreferenceKey(userId);
      return prefs.getBool(userKey) ?? false;
    } catch (e) {
      print('Error checking profile completion preference: $e');
      return false;
    }
  }

  /// Save user's preference to not be asked again
  Future<void> setDontAskAgain(String userId, bool dontAsk) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userKey = _getUserPreferenceKey(userId);
      await prefs.setBool(userKey, dontAsk);
    } catch (e) {
      print('Error saving profile completion preference: $e');
    }
  }

  /// Check which profile fields are missing
  List<String> getMissingProfileFields(AppUser user) {
    final missingFields = <String>[];
    
    // Check for missing profile image
    // For now, always consider profile image missing to ensure users can upload their own image
    // This is because Firebase Auth often provides default URLs that aren't actual user photos
    // TODO: In production, implement proper user-uploaded image detection
    missingFields.add('profileImage');
    
    // Check for missing full name
    if (user.displayName == null || user.displayName!.trim().isEmpty) {
      missingFields.add('displayName');
    }
    
    // Check for missing email
    if (user.email == null || user.email!.trim().isEmpty) {
      missingFields.add('email');
    }
    
    // Check for missing phone number
    if (user.phoneNumber == null || user.phoneNumber!.trim().isEmpty) {
      missingFields.add('phoneNumber');
    }
    
    // Company logo is always considered missing for now
    // (since we don't have company logo upload functionality)
    missingFields.add('companyLogo');
    
    return missingFields;
  }

  /// Check if profile completion should be shown
  Future<bool> shouldShowProfileCompletion(AppUser user) async {
    // Check if user has opted out
    if (await shouldSkipProfileCompletion(user.id)) {
      return false;
    }

    // Check if there are missing fields
    final missingFields = getMissingProfileFields(user);

    // Only show if there are missing critical fields (excluding company logo)
    final criticalMissingFields = missingFields.where((field) => field != 'companyLogo').toList();

    return criticalMissingFields.isNotEmpty;
  }

  /// Get user-friendly field names for display
  String getFieldDisplayName(String fieldName) {
    switch (fieldName) {
      case 'profileImage':
        return 'Profile Picture';
      case 'displayName':
        return 'Full Name';
      case 'email':
        return 'Email Address';
      case 'phoneNumber':
        return 'Phone Number';
      case 'companyLogo':
        return 'Company Logo';
      default:
        return fieldName;
    }
  }

  /// Get fallback values for missing fields
  Map<String, String> getFallbackValues(AppUser user) {
    return {
      'displayName': user.displayName?.isNotEmpty == true 
          ? user.displayName! 
          : 'Insurance Agent',
      'email': user.email?.isNotEmpty == true 
          ? user.email! 
          : '<EMAIL>',
      'phoneNumber': user.phoneNumber?.isNotEmpty == true 
          ? user.phoneNumber! 
          : '+91 - - - - - - - - - -',
    };
  }

  /// Reset profile completion preferences for a specific user (useful for testing)
  Future<void> resetPreferences(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userKey = _getUserPreferenceKey(userId);
      await prefs.remove(userKey);
    } catch (e) {
      print('Error resetting profile completion preferences: $e');
    }
  }

  /// Reset all profile completion preferences (useful for testing)
  Future<void> resetAllPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final profileKeys = keys.where((key) => key.startsWith(_dontAskAgainKeyPrefix));

      for (final key in profileKeys) {
        await prefs.remove(key);
      }
    } catch (e) {
      print('Error resetting all profile completion preferences: $e');
    }
  }

  /// Get all users who have opted out (useful for debugging)
  Future<List<String>> getUsersWhoOptedOut() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      final profileKeys = keys.where((key) => key.startsWith(_dontAskAgainKeyPrefix));

      final optedOutUsers = <String>[];
      for (final key in profileKeys) {
        if (prefs.getBool(key) == true) {
          final userId = key.substring(_dontAskAgainKeyPrefix.length);
          optedOutUsers.add(userId);
        }
      }

      return optedOutUsers;
    } catch (e) {
      print('Error getting opted out users: $e');
      return [];
    }
  }

  /// Debug method to print current preference status
  Future<void> debugPreferences() async {
    try {
      final optedOutUsers = await getUsersWhoOptedOut();
      print('Profile Completion Debug:');
      print('Users who opted out: ${optedOutUsers.length}');
      for (final userId in optedOutUsers) {
        print('  - User ID: $userId');
      }
    } catch (e) {
      print('Error debugging preferences: $e');
    }
  }

  /// Check if the photoURL is a default/placeholder image
  bool _isDefaultProfileImage(String photoURL) {
    final url = photoURL.trim().toLowerCase();

    // Common default profile image patterns
    final defaultPatterns = [
      'default',
      'placeholder',
      'avatar',
      'gravatar.com/avatar/00000',
      'ui-avatars.com',
      'robohash.org',
      'identicon',
      'blank',
      'empty',
    ];

    // Check if URL contains any default patterns
    for (final pattern in defaultPatterns) {
      if (url.contains(pattern)) {
        return true;
      }
    }

    // Check for very short URLs (likely placeholders)
    if (url.length < 10) {
      return true;
    }

    return false;
  }

  /// Check if the photoURL represents an actual user-uploaded image
  bool _isActualUserUploadedImage(String photoURL) {
    final url = photoURL.trim();

    // For now, we'll be conservative and assume most URLs are not user-uploaded
    // In a real app, you'd check if the URL points to your app's storage bucket

    // Check for common user-uploaded image patterns
    final userUploadPatterns = [
      'firebase',
      'cloudinary',
      'amazonaws',
      'googleusercontent',
      '/uploads/',
      '/profile/',
      '/avatar/',
    ];

    // If URL contains user upload patterns, consider it user-uploaded
    for (final pattern in userUploadPatterns) {
      if (url.toLowerCase().contains(pattern)) {
        return true;
      }
    }

    // For development/testing, consider local file paths as user-uploaded
    if (url.startsWith('/') || url.startsWith('file://')) {
      return true;
    }

    // Default to false - assume it's not a user-uploaded image
    return false;
  }

  /// Debug method to analyze user profile completeness
  void debugUserProfile(AppUser user) {
    print('=== Profile Completion Debug ===');
    print('User ID: ${user.id}');
    print('Display Name: ${user.displayName ?? "NULL"}');
    print('Email: ${user.email ?? "NULL"}');
    print('Phone: ${user.phoneNumber ?? "NULL"}');
    print('Photo URL: ${user.photoURL ?? "NULL"}');

    if (user.photoURL != null) {
      print('Photo URL Analysis:');
      print('  - Is Default: ${_isDefaultProfileImage(user.photoURL!)}');
      print('  - Is User Uploaded: ${_isActualUserUploadedImage(user.photoURL!)}');
    }

    final missingFields = getMissingProfileFields(user);
    print('Missing Fields: $missingFields');
    print('Should Show Completion: ${shouldShowProfileCompletion(user)}');
    print('================================');
  }

  /// Update user profile with new data
  /// This is a placeholder - in a real app, this would update Firebase/database
  Future<AppUser> updateUserProfile(AppUser currentUser, Map<String, String?> updates) async {
    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Create updated user object
      final updatedUser = currentUser.copyWith(
        displayName: updates['displayName'] ?? currentUser.displayName,
        email: updates['email'] ?? currentUser.email,
        phoneNumber: updates['phoneNumber'] ?? currentUser.phoneNumber,
        // Handle profile image path - in a real app, this would be uploaded to cloud storage
        photoURL: updates['profileImagePath'] ?? currentUser.photoURL,
      );
      
      // TODO: In a real implementation, update Firebase user profile
      // await FirebaseAuth.instance.currentUser?.updateDisplayName(updatedUser.displayName);
      // await FirebaseAuth.instance.currentUser?.updateEmail(updatedUser.email);
      // etc.
      
      return updatedUser;
    } catch (e) {
      print('Error updating user profile: $e');
      rethrow;
    }
  }

  /// Generate summary of what will be used in PDF
  String generatePDFPreviewSummary(AppUser user, Map<String, String?> updates) {
    final fallbacks = getFallbackValues(user);
    final finalName = updates['displayName']?.isNotEmpty == true 
        ? updates['displayName']! 
        : fallbacks['displayName']!;
    final finalEmail = updates['email']?.isNotEmpty == true 
        ? updates['email']! 
        : fallbacks['email']!;
    final finalPhone = updates['phoneNumber']?.isNotEmpty == true 
        ? updates['phoneNumber']! 
        : fallbacks['phoneNumber']!;
    
    return '''PDF will include:
• Name: $finalName
• Email: $finalEmail
• Phone: $finalPhone
• Profile Picture: ${user.photoURL?.isNotEmpty == true ? 'Your photo' : 'All About Insurance logo'}
• Company Logo: All About Insurance logo''';
  }
}

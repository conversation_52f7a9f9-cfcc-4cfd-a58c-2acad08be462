import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import '../models/user_model.dart';

class PDFGenerationService {
  static const double _pageMargin = 12.0;
  static const double _headerHeight = 117.0;
  
  /// Generates a PDF document with comparison of multiple products
  static Future<File> generateComparisonPDF({
    required String customerName,
    required AppUser user,
    required List<Map<String, dynamic>> comparisonProducts,
    required List<Map<String, String>> benefits,
    required Map<String, String> premiumDetails,
  }) async {
    final pdf = pw.Document();

    // Load app logo for watermark
    final appLogoBytes = await _loadAppLogo();

    // Build header with async profile picture
    final headerWidget = await _buildHeader(customerName, user);

    // Add page to PDF
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(_pageMargin),
        build: (pw.Context context) {
          return pw.Stack(
            children: [
              // Main content
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Header Section (15% of page)
                  headerWidget,
                  pw.SizedBox(height: 16),

                  // Content Section (85% of page)
                  pw.Expanded(
                    child: _buildComparisonContent(
                      comparisonProducts: comparisonProducts,
                      benefits: benefits,
                      premiumDetails: premiumDetails,
                      appLogoBytes: appLogoBytes,
                    ),
                  ),
                ],
              ),

              // Watermark on top of everything
              if (appLogoBytes != null) _buildWatermark(appLogoBytes),
            ],
          );
        },
      ),
    );

    // Save PDF to temporary directory
    final output = await getTemporaryDirectory();
    final fileName = _generateComparisonFileName(customerName);
    final file = File('${output.path}/$fileName');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  /// Generates a PDF document with product details and benefits comparison
  static Future<File> generateProductDetailsPDF({
    required String customerName,
    required AppUser user,
    required String companyName,
    required String productName,
    required String planName,
    required String sumInsured,
    required List<Map<String, String>> benefits,
    required Map<String, String> premiumDetails,
  }) async {
    final pdf = pw.Document();
    
    // Load app logo for watermark
    final appLogoBytes = await _loadAppLogo();

    // Build header with async profile picture
    final headerWidget = await _buildHeader(customerName, user);

    // Add page to PDF
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(_pageMargin),
        build: (pw.Context context) {
          return pw.Stack(
            children: [
              // Main content
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // Header Section (15% of page)
                  headerWidget,
                  pw.SizedBox(height: 16),

                  // Content Section (85% of page)
                  pw.Expanded(
                    child: _buildContent(
                      companyName: companyName,
                      productName: productName,
                      planName: planName,
                      sumInsured: sumInsured,
                      benefits: benefits,
                      premiumDetails: premiumDetails,
                      appLogoBytes: appLogoBytes,
                    ),
                  ),
                ],
              ),

              // Watermark on top of everything
              if (appLogoBytes != null) _buildWatermark(appLogoBytes),
            ],
          );
        },
      ),
    );
    
    // Save PDF to temporary directory
    final output = await getTemporaryDirectory();
    final fileName = _generateFileName(customerName);
    final file = File('${output.path}/$fileName');
    await file.writeAsBytes(await pdf.save());
    
    return file;
  }
  
  /// Builds the header section with user profile and customer info
  static Future<pw.Widget> _buildHeader(String customerName, AppUser user) async {
    final profilePicture = await _buildProfilePicture(user);
    return pw.Container(
      height: _headerHeight,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: const PdfColor.fromInt(0xFFF5F5F5),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // Left column (60% width) - User profile
          pw.Expanded(
            flex: 60,
            child: pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Profile picture with dynamic content
                profilePicture,
                pw.SizedBox(width: 12),

                // Text information column
                pw.Expanded(
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      // User's full name with enhanced fallback
                      pw.Text(
                        _getUserDisplayName(user),
                        style: pw.TextStyle(
                          fontSize: 16,
                          fontWeight: pw.FontWeight.bold,
                          color: const PdfColor.fromInt(0xFF111418),
                        ),
                      ),
                      pw.SizedBox(height: 4),

                      // User's designation/title
                      pw.Text(
                        'Senior Insurance Agent',
                        style: const pw.TextStyle(
                          fontSize: 10,
                          color: PdfColor.fromInt(0xFF637488),
                        ),
                      ),
                      pw.SizedBox(height: 6),

                      // User's contact number with enhanced fallback
                      pw.Text(
                        _getUserPhoneNumber(user),
                        style: const pw.TextStyle(
                          fontSize: 10,
                          color: PdfColor.fromInt(0xFF637488),
                        ),
                      ),
                      pw.SizedBox(height: 4),

                      // User's email address with enhanced fallback
                      pw.Text(
                        _getUserEmail(user),
                        style: const pw.TextStyle(
                          fontSize: 10,
                          color: PdfColor.fromInt(0xFF637488),
                        ),
                        overflow: pw.TextOverflow.clip,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // Right column (40% width) - Date and customer info
          pw.Expanded(
            flex: 40,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                // Current date
                pw.Text(
                  _formatDate(DateTime.now()),
                  style: pw.TextStyle(
                    fontSize: 8,
                    color: const PdfColor.fromInt(0xFF111418),
                  ),
                ),
                pw.SizedBox(height: 16),
                
                // Customer name field
                pw.Text(
                  'Created For:',
                  style: pw.TextStyle(
                    fontSize: 10,
                    color: const PdfColor.fromInt(0xFF111418),
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(
                  customerName,
                  style: pw.TextStyle(
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                    color: const PdfColor.fromInt(0xFFE92933),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  

  
  /// Helper method to format date in dd/mm/yyyy format
  static String _formatDate(DateTime date) {
    const List<String> months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    String formattedDate = '${date.day} ${months[date.month - 1]} ${date.year}';
    return formattedDate;
    // return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Helper method to format currency for PDF (replaces ₹ with Rs.)
  static String _formatCurrencyForPDF(String amount) {
    // Replace rupee symbol with Rs.
    return amount.replaceAll('₹', 'Rs. ');
  }

  /// Generates PDF filename based on customer name
  static String _generateFileName(String customerName) {
    // Extract first name from customer name
    String firstName = _extractFirstName(customerName);

    // Return formatted filename without timestamp for cleaner appearance
    return '$firstName - Quote from All About Insurance.pdf';
  }

  /// Extracts first name from customer name input
  static String _extractFirstName(String customerName) {
    // Handle null or empty cases
    if (customerName.trim().isEmpty) {
      return 'Customer';
    }

    // Handle "NA" case (case insensitive)
    if (customerName.trim().toLowerCase() == 'na') {
      return 'Customer';
    }

    // Split by spaces and take first part
    final nameParts = customerName.trim().split(' ');
    final firstName = nameParts.first.trim();

    // Return first name or fallback to "Customer"
    return firstName.isNotEmpty ? firstName : 'Customer';
  }

  /// Builds the content section with benefits comparison table
  static pw.Widget _buildContent({
    required String companyName,
    required String productName,
    required String planName,
    required String sumInsured,
    required List<Map<String, String>> benefits,
    required Map<String, String> premiumDetails,
    Uint8List? appLogoBytes,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Product Details Section
        // _buildProductDetailsSection(companyName, productName, planName, sumInsured),
        // pw.SizedBox(height: 16),

        // Benefits Comparison Table
        pw.Expanded(
          child: _buildBenefitsTable(companyName, productName, planName, sumInsured, benefits, appLogoBytes),
        ),

        pw.SizedBox(height: 16),

        // Premium Information
        _buildPremiumSection(premiumDetails),

        pw.SizedBox(height: 12),

        // Disclaimer
        _buildDisclaimerSection(),

        pw.SizedBox(height: 16),

        // Footer
        _buildFooter(),
      ],
    );
  }

  /// Builds the product details section
  //static pw.Widget _buildProductDetailsSection(
  //  String companyName,
  //  String productName,
  //  String planName,
  //  String sumInsured,
  //) {
  //  return pw.Container(
  //      padding: const pw.EdgeInsets.all(12),
  //      decoration: pw.BoxDecoration(
  //      color: const PdfColor.fromInt(0xFFF8F9FA),
  //      borderRadius: pw.BorderRadius.circular(8),
  //      border: pw.Border.all(color: const PdfColor.fromInt(0xFFE0E0E0)),
  //    ),
  //    child: pw.Column(
  //        crossAxisAlignment: pw.CrossAxisAlignment.start,
  //      children: [
  //        pw.Text(
  //          'Product Details',
  //          style: pw.TextStyle(
  //            fontSize: 16,
  //            fontWeight: pw.FontWeight.bold,
  //            color: const PdfColor.fromInt(0xFF111418),
  //          ),
  //        ),
  //        pw.SizedBox(height: 8),
  //        pw.Row(
  //          children: [
  //            pw.Expanded(
  //              child: _buildDetailItem('Company', companyName),
  //            ),
  //            pw.SizedBox(width: 16),
  //            pw.Expanded(
  //              child: _buildDetailItem('Product', productName),
  //            ),
  //          ],
  //        ),
  //        pw.SizedBox(height: 8),
  //        pw.Row(
  //          children: [
  //            pw.Expanded(
  //              child: _buildDetailItem('Plan', planName),
  //            ),
  //            pw.SizedBox(width: 16),
  //            pw.Expanded(
  //              child: _buildDetailItem('Sum Insured', sumInsured),
  //            ),
  //          ],
  //        ),
  //      ],
  //    ),
  //  );
  //}

  /// Builds a detail item for product details
  //static pw.Widget _buildDetailItem(String label, String value) {
  //  return pw.Column(
  //    crossAxisAlignment: pw.CrossAxisAlignment.start,
  //    children: [
  //      pw.Text(
  //        label,
  //        style: const pw.TextStyle(
  //          fontSize: 10,
  //          color: PdfColor.fromInt(0xFF637488),
  //        ),
  //      ),
  //      pw.SizedBox(height: 2),
  //      pw.Text(
  //        value,
  //        style: pw.TextStyle(
  //          fontSize: 12,
  //          fontWeight: pw.FontWeight.bold,
  //          color: const PdfColor.fromInt(0xFF111418),
  //        ),
  //      ),
  //    ],
  //  );
  //}

  /// Builds the benefits comparison table
  static pw.Widget _buildBenefitsTable(
    String companyName,
    String productName,
    String planName,
    String sumInsured,
    List<Map<String, String>> benefits,
    Uint8List? appLogoBytes,
  ) {
    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: const PdfColor.fromInt(0xFFE0E0E0)),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Table(
        border: pw.TableBorder(
          horizontalInside: const pw.BorderSide(color: PdfColor.fromInt(0xFFE0E0E0)),
          verticalInside: const pw.BorderSide(color: PdfColor.fromInt(0xFFE0E0E0)),
        ),
        columnWidths: const {
          0: pw.FlexColumnWidth(2),
          1: pw.FlexColumnWidth(3),
        },
        children: [
          // Header row
          pw.TableRow(
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFF8F9FA),
            ),
            children: [
              _buildTableHeader('Benefit'),
              _buildTableHeaderWithProduct(companyName, productName, planName, sumInsured, appLogoBytes),
            ],
          ),
          // Data rows
          ...benefits.map((benefit) => pw.TableRow(
            children: [
              _buildTableCell(benefit['type'] ?? 'Unknown'),
              _buildTableCell(_formatCurrencyForPDF(benefit['combinedDescription'] ?? 'No description available')),
            ],
          )),
        ],
      ),
    );
  }

  /// Builds a table header cell
  static pw.Widget _buildTableHeader(String text) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Align(
        alignment: pw.Alignment.centerLeft,
        child: pw.Text(
          text,
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: const PdfColor.fromInt(0xFF637488),
          ),
        ),
      ),
    );
  }

  /// Builds the structured table header with product information
  static pw.Widget _buildTableHeaderWithProduct(
    String companyName,
    String productName,
    String planName,
    String sumInsured,
    [Uint8List? appLogoBytes]
  ) {
    return pw.Container(
      height: 80,
      padding: const pw.EdgeInsets.all(8),
      child: pw.Row(
        children: [
          // Left Section (40% of column width) - Company Logo
          pw.Expanded(
            flex: 40,
            child: _buildCompanyLogo(companyName, appLogoBytes),
          ),
          pw.SizedBox(width: 8),

          // Right Section (60% of column width) - Product Details
          pw.Expanded(
            flex: 60,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  productName,
                  style: pw.TextStyle(
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                    color: const PdfColor.fromInt(0xFF111418),
                  ),
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  planName,
                  style: const pw.TextStyle(
                    fontSize: 11,
                    color: PdfColor.fromInt(0xFF637488),
                  ),
                ),
                pw.SizedBox(height: 2),
                pw.Text(
                  _formatCurrencyForPDF(sumInsured),
                  style: const pw.TextStyle(
                    fontSize: 11,
                    color: PdfColor.fromInt(0xFF086788),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Builds a table cell
  static pw.Widget _buildTableCell(String text) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Text(
        text,
        style: const pw.TextStyle(
          fontSize: 12,
          color: PdfColor.fromInt(0xFF111418),
        ),
      ),
    );
  }

  /// Builds company logo with "All About Insurance" text fallback
  static pw.Widget _buildCompanyLogo(String companyName, Uint8List? appLogoBytes) {
    // For now, always use "All About Insurance" text as fallback
    // In a production app, you'd check for actual company logos first
    return pw.Container(
      height: 50,
      decoration: pw.BoxDecoration(
        color: const PdfColor.fromInt(0xFFE92933),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Center(
        child: pw.Text(
          'All About\nInsurance',
          style: pw.TextStyle(
            fontSize: 10,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.white,
          ),
          textAlign: pw.TextAlign.center,
        ),
      ),
    );
  }

  /// Builds the premium information section
  static pw.Widget _buildPremiumSection(Map<String, String> premiumDetails) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: const PdfColor.fromInt(0xFFFDFDFD), // More transparent background
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: const PdfColor.fromInt(0xFFE0E0E0)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Premium Information',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: const PdfColor.fromInt(0xFF111418),
            ),
          ),
          pw.SizedBox(height: 8),
          ...premiumDetails.entries.map((entry) => pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 4),
            child: pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                  entry.key,
                  style: const pw.TextStyle(
                    fontSize: 12,
                    color: PdfColor.fromInt(0xFF111418),
                  ),
                ),
                pw.Text(
                  _formatCurrencyForPDF(entry.value),
                  style: pw.TextStyle(
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                    color: const PdfColor.fromInt(0xFF111418),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// Builds the disclaimer section
  static pw.Widget _buildDisclaimerSection() {
    return pw.Container(
      padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: pw.Text(
        'The price quoted for the policy may vary at the time of purchase as it is subject to the insurance company\'s terms and conditions.',
        style: pw.TextStyle(
          fontSize: 10,
          fontStyle: pw.FontStyle.italic,
          color: const PdfColor.fromInt(0xFF637488),
        ),
        textAlign: pw.TextAlign.left,
      ),
    );
  }

  /// Builds the footer section
  static pw.Widget _buildFooter() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Center(
        child: pw.Text(
          'Generated by All About Insurance App - ${_formatDate(DateTime.now())}',
          style: const pw.TextStyle(
            fontSize: 10,
            color: PdfColor.fromInt(0xFF637488),
          ),
        ),
      ),
    );
  }

  /// Loads app logo from assets for watermark and profile picture
  static Future<Uint8List?> _loadAppLogo() async {
    try {
      final ByteData data = await rootBundle.load('assets/logo/app-logo.png');
      return data.buffer.asUint8List();
    } catch (e) {
      print('Error loading app logo: $e');
      return null;
    }
  }

  /// Builds watermark background
  static pw.Widget _buildWatermark(Uint8List logoBytes) {
    return pw.Positioned.fill(
      child: pw.Center(
        child: pw.Opacity(
          opacity: 0.1,
          child: pw.Image(
            pw.MemoryImage(logoBytes),
            width: 200,
            height: 200,
          ),
        ),
      ),
    );
  }

  /// Builds profile picture with dynamic content
  static Future<pw.Widget> _buildProfilePicture(AppUser user) async {
    // Try to load user profile image if available
    if (user.photoURL != null && user.photoURL!.isNotEmpty) {
      try {
        // In a production app, you'd want to download and cache the user's profile image
        // For now, we'll fall back to text since network image loading requires additional setup
        print('User has profile URL: ${user.photoURL}');
      } catch (e) {
        print('Error loading user profile image: $e');
      }
    }

    // Fallback to "All About Insurance" text
    return _buildDefaultProfileContainer(user);
  }



  /// Builds default profile container with "All About Insurance" text
  static pw.Widget _buildDefaultProfileContainer(AppUser user) {
    return pw.Container(
      width: 85,
      height: 85,
      decoration: pw.BoxDecoration(
        color: const PdfColor.fromInt(0xFFE92933),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Center(
        child: pw.Text(
          'All About\nInsurance',
          style: pw.TextStyle(
            color: PdfColors.white,
            fontSize: 15,
            fontWeight: pw.FontWeight.bold,
          ),
          textAlign: pw.TextAlign.center,
        ),
      ),
    );
  }

  /// Helper methods for enhanced user data with fallbacks

  /// Gets user display name with professional fallback
  static String _getUserDisplayName(AppUser user) {
    if (user.displayName != null && user.displayName!.trim().isNotEmpty) {
      return user.displayName!.trim();
    }
    return 'Insurance Agent';
  }

  /// Gets user email with professional fallback
  static String _getUserEmail(AppUser user) {
    if (user.email != null && user.email!.trim().isNotEmpty) {
      return user.email!.trim();
    }
    return '<EMAIL>';
  }

  /// Gets user phone number with formatted fallback
  static String _getUserPhoneNumber(AppUser user) {
    if (user.phoneNumber != null && user.phoneNumber!.trim().isNotEmpty) {
      return user.phoneNumber!.trim();
    }
    return '+91 - - - - - - - - - -';
  }

  /// Builds the comparison content section with multiple products
  static pw.Widget _buildComparisonContent({
    required List<Map<String, dynamic>> comparisonProducts,
    required List<Map<String, String>> benefits,
    required Map<String, String> premiumDetails,
    Uint8List? appLogoBytes,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // Benefits Comparison Table
        pw.Expanded(
          child: _buildComparisonTable(comparisonProducts, benefits, appLogoBytes),
        ),

        pw.SizedBox(height: 16),

        // Premium Information Section
        _buildPremiumInformationSection(premiumDetails),

        pw.SizedBox(height: 12),

        // Disclaimer
        _buildDisclaimerSection(),

        pw.SizedBox(height: 16),

        // Footer
        _buildFooter(),
      ],
    );
  }

  /// Builds the comparison table with multiple products
  static pw.Widget _buildComparisonTable(
    List<Map<String, dynamic>> comparisonProducts,
    List<Map<String, String>> benefits,
    Uint8List? appLogoBytes,
  ) {
    // Optimized column widths: 18% for benefits, remaining 82% divided among products
    final benefitsColumnWidth = 18.0;
    final productColumnWidth = comparisonProducts.isNotEmpty
        ? (82.0 / comparisonProducts.length)
        : 82.0;

    final columnWidths = <int, pw.TableColumnWidth>{
      0: pw.FlexColumnWidth(benefitsColumnWidth), // Reduced benefits column width
    };

    // Add product columns with more space
    for (int i = 0; i < comparisonProducts.length; i++) {
      columnWidths[i + 1] = pw.FlexColumnWidth(productColumnWidth);
    }

    return pw.Container(
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: const PdfColor.fromInt(0xFFE0E0E0)),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Table(
        border: pw.TableBorder(
          horizontalInside: const pw.BorderSide(color: PdfColor.fromInt(0xFFE0E0E0)),
          verticalInside: const pw.BorderSide(color: PdfColor.fromInt(0xFFE0E0E0)),
        ),
        columnWidths: columnWidths,
        defaultVerticalAlignment: pw.TableCellVerticalAlignment.top,
        children: [
          // Header row with existing structure preserved
          pw.TableRow(
            decoration: const pw.BoxDecoration(
              color: PdfColor.fromInt(0xFFF8F9FA),
            ),
            children: [
              _buildTableHeader('Benefit'),
              ...comparisonProducts.map((product) =>
                _buildTableHeaderWithProduct(
                  product['company'] ?? 'Unknown Company',
                  product['product'] ?? 'Unknown Product',
                  product['plan'] ?? 'Unknown Plan',
                  product['sumInsured'] ?? 'Unknown Coverage',
                  appLogoBytes,
                )
              ),
            ],
          ),
          // Data rows
          ...benefits.map((benefit) => pw.TableRow(
            children: [
              _buildTableCell(benefit['type'] ?? 'Unknown'),
              ...comparisonProducts.map((product) =>
                _buildTableCell(_formatCurrencyForPDF(benefit['combinedDescription'] ?? 'No description available'))
              ),
            ],
          )),
        ],
      ),
    );
  }

  /// Builds the premium information section
  static pw.Widget _buildPremiumInformationSection(Map<String, String> premiumDetails) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: const PdfColor.fromInt(0xFFF8F9FA),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: const PdfColor.fromInt(0xFFE0E0E0)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Premium Information',
            style: pw.TextStyle(
              fontSize: 14,
              fontWeight: pw.FontWeight.bold,
              color: const PdfColor.fromInt(0xFF111418),
            ),
          ),
          pw.SizedBox(height: 12),
          ...premiumDetails.entries.map((entry) =>
            pw.Padding(
              padding: const pw.EdgeInsets.only(bottom: 6),
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    entry.key,
                    style: const pw.TextStyle(
                      fontSize: 11,
                      color: PdfColor.fromInt(0xFF637488),
                    ),
                  ),
                  pw.Text(
                    _formatCurrencyForPDF(entry.value),
                    style: pw.TextStyle(
                      fontSize: 11,
                      fontWeight: pw.FontWeight.bold,
                      color: const PdfColor.fromInt(0xFF111418),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Generates comparison PDF filename
  static String _generateComparisonFileName(String customerName) {
    // Extract first name from customer name
    String firstName = _extractFirstName(customerName);

    // Return formatted filename for comparison
    return '$firstName - Comparison Report from All About Insurance.pdf';
  }


}

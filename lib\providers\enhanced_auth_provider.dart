import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/enhanced_auth_service.dart';
import '../services/supabase_profile_service.dart';
import '../models/user_model.dart';
import '../models/user_role.dart';

// Enhanced Auth Service Provider
final enhancedAuthServiceProvider = Provider<EnhancedAuthService>((ref) {
  return EnhancedAuthService();
});

// Supabase Profile Service Provider
final supabaseProfileServiceProvider = Provider<SupabaseProfileService>((ref) {
  return SupabaseProfileService();
});

// Firebase Auth State Provider
final firebaseAuthStateProvider = StreamProvider<User?>((ref) {
  final authService = ref.watch(enhancedAuthServiceProvider);
  return authService.authStateChanges;
});

// Current User Provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(firebaseAuthStateProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// User Profile Provider (with role information)
final userProfileProvider = FutureProvider<AppUser?>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return null;

  final profileService = ref.watch(supabaseProfileServiceProvider);
  return await profileService.getProfileWithRole(user.uid);
});

// Available Roles Provider
final availableRolesProvider = FutureProvider<List<UserRole>>((ref) async {
  final profileService = ref.watch(supabaseProfileServiceProvider);
  return await profileService.getAvailableRoles();
});

// User Permissions Provider
final userPermissionsProvider = FutureProvider.family<bool, String>((ref, permission) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return false;

  final profileService = ref.watch(supabaseProfileServiceProvider);
  return await profileService.hasPermission(user.uid, permission);
});

// Authentication State Notifier
class AuthStateNotifier extends StateNotifier<AuthState> {
  final EnhancedAuthService _authService;
  final SupabaseProfileService _profileService;

  AuthStateNotifier(this._authService, this._profileService) : super(AuthState.initial());

  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          user: result.user,
          message: result.message,
          profileCreated: result.profileCreated,
          profileError: result.profileError,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          user: result.user,
          message: result.message,
          profileCreated: result.profileCreated,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authService.signInWithGoogle();

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          user: result.user,
          message: result.message,
          profileCreated: result.profileCreated,
          profileError: result.profileError,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true);
    
    try {
      await _authService.signOut();
      state = AuthState.initial();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to sign out: ${e.toString()}',
      );
    }
  }

  /// Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authService.sendPasswordResetEmail(email);

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          message: result.message,
        );
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.message,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'An unexpected error occurred: ${e.toString()}',
      );
    }
  }

  /// Ensure current user has a profile
  Future<void> ensureProfileExists() async {
    try {
      final success = await _authService.ensureProfileExists();
      state = state.copyWith(profileCreated: success);
    } catch (e) {
      state = state.copyWith(
        profileError: 'Failed to ensure profile exists: ${e.toString()}',
      );
    }
  }

  /// Clear error and message states
  void clearMessages() {
    state = state.copyWith(error: null, message: null);
  }
}

// Auth State Notifier Provider
final authStateNotifierProvider = StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  final authService = ref.watch(enhancedAuthServiceProvider);
  final profileService = ref.watch(supabaseProfileServiceProvider);
  return AuthStateNotifier(authService, profileService);
});

/// Authentication State Class
class AuthState {
  final bool isLoading;
  final User? user;
  final String? error;
  final String? message;
  final bool profileCreated;
  final String? profileError;

  const AuthState({
    required this.isLoading,
    this.user,
    this.error,
    this.message,
    required this.profileCreated,
    this.profileError,
  });

  factory AuthState.initial() {
    return const AuthState(
      isLoading: false,
      profileCreated: false,
    );
  }

  AuthState copyWith({
    bool? isLoading,
    User? user,
    String? error,
    String? message,
    bool? profileCreated,
    String? profileError,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error,
      message: message,
      profileCreated: profileCreated ?? this.profileCreated,
      profileError: profileError,
    );
  }

  bool get isAuthenticated => user != null;
  bool get hasProfileIssue => isAuthenticated && !profileCreated && profileError != null;
}

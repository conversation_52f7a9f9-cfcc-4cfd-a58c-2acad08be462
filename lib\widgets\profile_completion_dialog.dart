import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../models/user_model.dart';
import '../services/profile_completion_service.dart';

class ProfileCompletionDialog extends ConsumerStatefulWidget {
  final AppUser user;
  final Function(Map<String, String?>) onComplete;
  final VoidCallback onSkip;

  const ProfileCompletionDialog({
    super.key,
    required this.user,
    required this.onComplete,
    required this.onSkip,
  });

  @override
  ConsumerState<ProfileCompletionDialog> createState() => _ProfileCompletionDialogState();
}

class _ProfileCompletionDialogState extends ConsumerState<ProfileCompletionDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  
  bool _isLoading = false;
  bool _dontAskAgain = false;
  List<String> _missingFields = [];
  File? _selectedProfileImage;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _initializeFields() {
    // Identify missing fields and pre-populate existing ones
    _missingFields = [];
    
    if (widget.user.displayName == null || widget.user.displayName!.trim().isEmpty) {
      _missingFields.add('name');
    } else {
      _nameController.text = widget.user.displayName!;
    }
    
    if (widget.user.email == null || widget.user.email!.trim().isEmpty) {
      _missingFields.add('email');
    } else {
      _emailController.text = widget.user.email!;
    }
    
    if (widget.user.phoneNumber == null || widget.user.phoneNumber!.trim().isEmpty) {
      _missingFields.add('phone');
    } else {
      _phoneController.text = widget.user.phoneNumber!;
    }
    
    // Check for profile image using the same logic as ProfileCompletionService
    final profileService = ProfileCompletionService.instance;
    final allMissingFields = profileService.getMissingProfileFields(widget.user);

    if (allMissingFields.contains('profileImage')) {
      _missingFields.add('profileImage');
    }

    // Company logo is always considered missing for now
    // (since we don't have company logo upload functionality in this dialog)
    _missingFields.add('companyLogo');
  }

  void _handleComplete() async {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Prepare updated profile data
        final updatedData = <String, String?>{
          'displayName': _nameController.text.trim().isNotEmpty ? _nameController.text.trim() : null,
          'email': _emailController.text.trim().isNotEmpty ? _emailController.text.trim() : null,
          'phoneNumber': _phoneController.text.trim().isNotEmpty
              ? '+91${_phoneController.text.trim().replaceAll(RegExp(r'[^\d]'), '')}'
              : null,
          'profileImagePath': _selectedProfileImage?.path,
        };

        // Save "don't ask again" preference if checked
        if (_dontAskAgain) {
          await _saveDontAskAgainPreference();
        }

        await Future.delayed(const Duration(milliseconds: 500)); // Small delay for UX
        widget.onComplete(updatedData);
        
        if (mounted) {
          Navigator.of(context).pop();
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('An error occurred. Please try again.'),
              backgroundColor: Color(0xFFe92933),
            ),
          );
        }
      }
    }
  }

  void _handleSkip() async {
    if (_dontAskAgain) {
      await _saveDontAskAgainPreference();
    }
    widget.onSkip();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  Future<void> _saveDontAskAgainPreference() async {
    try {
      await ProfileCompletionService.instance.setDontAskAgain(widget.user.id, true);
    } catch (e) {
      print('Error saving dont ask again preference: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      title: const Text(
        'Complete Your Profile (Optional)',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF111418),
        ),
      ),
      contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
      content: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.6, // Limit height to 60% of screen
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              // Scrollable content area
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
              // const Text(
              //   'Complete your profile for professional PDFs',
              //   style: TextStyle(
              //     fontSize: 14,
              //     color: Color(0xFF637488),
              //   ),
              // ),
              // const SizedBox(height: 16),
              
              // Profile Picture Section (if missing)
              if (_missingFields.contains('profileImage'))
                Column(
                  children: [
                    _buildProfilePictureSection(),
                    const SizedBox(height: 16),
                  ],
                ),

              // Show only missing fields
              if (_missingFields.contains('name'))
                Column(
                  children: [
                    TextFormField(
                      controller: _nameController,
                      textCapitalization: TextCapitalization.words,
                      decoration: const InputDecoration(
                        labelText: 'Name',
                        hintText: 'Enter full name',
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFe92933)),
                        ),
                        labelStyle: TextStyle(color: Color(0xFF637488)),
                        prefixIcon: Icon(
                          Icons.person,
                          color: Color(0xFFe92933),
                        ),
                      ),
                      validator: (value) {
                        // Optional validation - allow empty
                        if (value != null && value.trim().isNotEmpty && value.trim().length < 2) {
                          return 'Name must be at least 2 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              
              if (_missingFields.contains('email'))
                Column(
                  children: [
                    TextFormField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: const InputDecoration(
                        labelText: 'Email Address',
                        hintText: '<EMAIL>',
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFe92933)),
                        ),
                        labelStyle: TextStyle(color: Color(0xFF637488)),
                        prefixIcon: Icon(
                          Icons.email,
                          color: Color(0xFFe92933),
                        ),
                      ),
                      validator: (value) {
                        // Optional validation - allow empty
                        if (value != null && value.trim().isNotEmpty) {
                          final email = value.trim();
                          // More comprehensive email validation
                          if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email)) {
                            return 'Please enter a valid email address';
                          }
                          if (email.length > 254) {
                            return 'Email address is too long';
                          }
                          // Check for common invalid patterns
                          if (email.startsWith('.') || email.endsWith('.') ||
                              email.contains('..') || email.contains('@.') ||
                              email.contains('.@')) {
                            return 'Please enter a valid email address';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              
              if (_missingFields.contains('phone'))
                Column(
                  children: [
                    TextFormField(
                      controller: _phoneController,
                      keyboardType: TextInputType.phone,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(10),
                        _PhoneNumberFormatter(),
                      ],
                      decoration: const InputDecoration(
                        labelText: 'Mobile Number',
                        hintText: ' x x x x x   x x x x x',
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Color(0xFFe92933)),
                        ),
                        labelStyle: TextStyle(color: Color(0xFF637488)),
                        helperStyle: TextStyle(color: Color(0xFF637488), fontSize: 12),
                        prefixIcon: Icon(
                          Icons.phone,
                          color: Color(0xFFe92933),
                        ),
                        prefixText: '+91 ',
                        prefixStyle: TextStyle(color: Color(0xFF111418), fontWeight: FontWeight.w500),
                      ),
                      validator: (value) {
                        // Optional validation - allow empty
                        if (value != null && value.trim().isNotEmpty) {
                          final phone = value.trim().replaceAll(RegExp(r'[^\d]'), '');
                          if (phone.length != 10) {
                            return 'Please enter a valid 10-digit mobile number';
                          }
                          if (!RegExp(r'^[6-9]\d{9}$').hasMatch(phone)) {
                            return 'Please enter a valid Indian mobile number';
                          }
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 12),
                  ],
                ),
              
              // Don't ask again checkbox
              Row(
                children: [
                  Checkbox(
                    value: _dontAskAgain,
                    onChanged: (value) {
                      setState(() {
                        _dontAskAgain = value ?? false;
                      });
                    },
                    activeColor: const Color(0xFFe92933),
                  ),
                  const Expanded(
                    child: Text(
                      "Don't ask me again",
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF637488),
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              const Text(
                'Note: If you skip, placeholder information will be used in the PDF.',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF637488),
                  fontStyle: FontStyle.italic,
                ),
              ),
                    ],
                  ),
                ),
              ),
              // Fixed action buttons area
              Padding(
                padding: const EdgeInsets.fromLTRB(0, 16, 0, 16), // Add bottom padding
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: _isLoading ? null : _handleSkip,
                      child: const Text(
                        'Skip',
                        style: TextStyle(color: Color(0xFF637488)),
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _handleComplete,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFFe92933),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isLoading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text('Update Profile'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),

    );
  }

  /// Builds the profile picture selection section
  Widget _buildProfilePictureSection() {
    return Stack(
      children: [
        // Main container with border
        Container(
          margin: const EdgeInsets.only(top: 8),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFF637488)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(12, 16, 12, 16),
            child: Column(
              children: [
          Row(
            children: [
              // Profile picture preview
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _selectedProfileImage != null
                      ? Colors.transparent
                      : const Color(0xFFf1f1f1),
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(color: const Color(0xFFe92933)),
                ),
                child: _selectedProfileImage != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(30),
                        child: Image.file(
                          _selectedProfileImage!,
                          fit: BoxFit.cover,
                        ),
                      )
                    : const Icon(
                        Icons.person,
                        size: 30,
                        color: Color(0xFFe92933),
                      ),
              ),
              const SizedBox(width: 16),
              // Upload button
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectProfileImage,
                  icon: const Icon(
                    Icons.camera_alt,
                    size: 18,
                    color: Color(0xFF637488),
                  ),
                  label: Text(
                    _selectedProfileImage != null ? 'Change Photo' : 'Select Photo',
                    style: const TextStyle(
                      color: Color(0xFF637488),
                      fontSize: 14,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: const BorderSide(color: Colors.white),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Padding(
          //   padding: const EdgeInsets.only(top: 8),
          //   child: Text(
          //     _selectedProfileImage == null
          //         ? 'Optional: Add a professional photo for your PDFs'
          //         : 'Professional photo selected for your PDFs',
          //     style: TextStyle(
          //       fontSize: 12,
          //       color: _selectedProfileImage == null
          //           ? const Color(0xFF637488)
          //           : const Color(0xFF4CAF50),
          //       fontStyle: FontStyle.italic,
          //     ),
          //   ),
          // ),
              ],
            ),
          ),
        ),
        // Floating label positioned on the border
        Positioned(
          left: 12,
          top: 0,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6), // Increased padding for better coverage
            decoration: BoxDecoration(
              color: const Color(0xFFe7e8ed), // Match dialog background exactly
              borderRadius: BorderRadius.circular(2), // Slight rounding for smoother appearance
            ),
            child: const Text(
              'Profile Picture',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF637488),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Handles profile image selection
  Future<void> _selectProfileImage() async {
    try {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (BuildContext context) {
          return SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Select Profile Picture',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF111418),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _pickImageFromCamera();
                          },
                          icon: const Icon(Icons.camera_alt, color: Color(0xFFe92933)),
                          label: const Text(
                            'Camera',
                            style: TextStyle(color: Color(0xFFe92933)),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Color(0xFFe92933)),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _pickImageFromGallery();
                          },
                          icon: const Icon(Icons.photo_library, color: Color(0xFFe92933)),
                          label: const Text(
                            'Gallery',
                            style: TextStyle(color: Color(0xFFe92933)),
                          ),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Color(0xFFe92933)),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(color: Color(0xFF637488)),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    } catch (e) {
      print('Error showing image picker: $e');
    }
  }

  /// Pick image from camera
  Future<void> _pickImageFromCamera() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedProfileImage = File(image.path);
        });
      }
    } catch (e) {
      print('Error picking image from camera: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error accessing camera. Please try again.'),
            backgroundColor: Color(0xFFe92933),
          ),
        );
      }
    }
  }

  /// Pick image from gallery
  Future<void> _pickImageFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedProfileImage = File(image.path);
        });
      }
    } catch (e) {
      print('Error picking image from gallery: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error accessing gallery. Please try again.'),
            backgroundColor: Color(0xFFe92933),
          ),
        );
      }
    }
  }

  /// Shows the profile completion dialog
  static Future<void> show(
    BuildContext context, {
    required AppUser user,
    required Function(Map<String, String?>) onComplete,
    required VoidCallback onSkip,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return ProfileCompletionDialog(
          user: user,
          onComplete: onComplete,
          onSkip: onSkip,
        );
      },
    );
  }
}

/// Custom formatter for phone numbers
class _PhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Remove all non-digit characters
    final digitsOnly = newValue.text.replaceAll(RegExp(r'[^\d]'), '');

    // Limit to 10 digits
    final limitedDigits = digitsOnly.length > 10
        ? digitsOnly.substring(0, 10)
        : digitsOnly;

    // Format the number
    String formatted = limitedDigits;
    if (limitedDigits.length > 5) {
      formatted = '${limitedDigits.substring(0, 5)} ${limitedDigits.substring(5)}';
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

# Supabase Profiles Table Documentation (Enhanced)

## Overview

This document describes the enhanced profiles table schema for the All About Insurance app, featuring normalized role management and comprehensive data validation. The schema is designed to store all user profile data in Supabase while maintaining security, performance, and data integrity.

## Key Enhancements

### 🎯 **Normalized Role Management**
- Dedicated `roles` table for scalable role management
- Role-based permissions stored as JSON
- Organizational hierarchy support
- Consistent role naming and management

### 🛡️ **Comprehensive Data Validation**
- SQL-level constraints for data integrity
- Email format validation
- Phone number format validation
- Indian GSTIN format validation
- Date validation and business rules

## Table Structure

## Roles Table

The `roles` table provides normalized role management with rich metadata:

| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `id` | UUID | Primary key (auto-generated) | System |
| `role_name` | TEXT | Human-readable role name | "Insurance Agent" |
| `role_code` | TEXT | Programmatic identifier | "AGENT" |
| `description` | TEXT | Role description | "Licensed insurance sales representative" |
| `permissions` | JSONB | Role-specific permissions | `{"can_create_quotes": true}` |
| `hierarchy_level` | INTEGER | Organizational level (1=lowest) | 1, 2, 3, 4 |
| `is_active` | BOOLEAN | Role status | TRUE/FALSE |
| `created_at` | TIMESTAMPTZ | Creation timestamp | System |
| `updated_at` | TIMESTAMPTZ | Last update timestamp | System |

### Pre-defined Roles

| Role Name | Code | Level | Key Permissions |
|-----------|------|-------|----------------|
| Insurance Agent | AGENT | 1 | Create quotes, view policies, generate PDFs |
| Insurance Broker | BROKER | 2 | Manage clients, compare policies |
| Claims Manager | CLAIMS_MGR | 3 | Process claims, approve settlements |
| Regional Manager | REGIONAL_MGR | 4 | Manage agents, view reports |
| Underwriter | UNDERWRITER | 3 | Assess risks, approve policies |
| Customer Service Rep | CSR | 1 | View policies, update customer info |

## Profiles Table

### Primary Fields

| Field | Type | Description | Source |
|-------|------|-------------|---------|
| `id` | UUID | Primary key (auto-generated) | System |
| `firebase_uid` | TEXT | Firebase Auth UID (unique identifier) | Firebase Auth |
| `email` | TEXT | User email address | Firebase Auth / User Input |
| `is_email_verified` | BOOLEAN | Email verification status | Firebase Auth |
| `auth_provider` | TEXT | Authentication method used | Firebase Auth |

### Personal Information

| Field | Type | Description | Source |
|-------|------|-------------|---------|
| `display_name` | TEXT | User's full name | Firebase Auth / Profile Form |
| `phone_number` | TEXT | User's phone number | Firebase Auth / Profile Form |
| `date_of_birth` | DATE | User's date of birth | Profile Form |
| `photo_url` | TEXT | Profile picture URL | Cloud Storage |
| `company_logo_url` | TEXT | Company logo URL | Cloud Storage |

### Professional Information

| Field | Type | Description | Source |
|-------|------|-------------|---------|
| `company_name` | TEXT | Company/Organization name | Profile Form |
| `designation` | TEXT | Job title/position | Profile Form |
| `role_id` | UUID | Foreign key to roles table | Profile Form |
| `gstin` | TEXT | GST Identification Number (validated) | Profile Form |
| `state_of_residence` | TEXT | State where user resides | Profile Form |
| `native_language` | TEXT | Preferred language | Profile Form |

### App Preferences

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `push_notifications_enabled` | BOOLEAN | TRUE | Push notification preference |
| `email_notifications_enabled` | BOOLEAN | FALSE | Email notification preference |
| `location_services_enabled` | BOOLEAN | TRUE | Location access preference |
| `analytics_enabled` | BOOLEAN | FALSE | Analytics sharing preference |
| `dark_mode_enabled` | BOOLEAN | FALSE | Dark mode preference |
| `biometric_enabled` | BOOLEAN | TRUE | Biometric authentication preference |
| `auto_download_enabled` | BOOLEAN | FALSE | Auto-download preference |
| `profile_completion_dont_ask_again` | BOOLEAN | FALSE | Skip profile completion prompts |

### Subscription Information

| Field | Type | Description |
|-------|------|-------------|
| `subscription_plan` | TEXT | Plan type: basic, pro, enterprise |
| `subscription_status` | TEXT | Status: active, inactive, cancelled, expired |
| `subscription_start_date` | TIMESTAMPTZ | Subscription start date |
| `subscription_end_date` | TIMESTAMPTZ | Subscription end date |
| `billing_period` | TEXT | Billing cycle: monthly, annual |

### System Fields

| Field | Type | Description |
|-------|------|-------------|
| `created_at` | TIMESTAMPTZ | Profile creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp (auto-updated) |
| `last_sign_in_at` | TIMESTAMPTZ | Last sign-in timestamp |
| `deleted_at` | TIMESTAMPTZ | Soft delete timestamp (NULL = active) |

## Data Validation

The enhanced schema includes comprehensive SQL-level validation:

### Email Validation
- **Pattern**: `^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$`
- **Examples**: ✅ `<EMAIL>` ❌ `user@@gmail.com`

### Phone Number Validation
- **Pattern**: `^\+?[1-9]\d{1,14}$`
- **Examples**: ✅ `+919876543210` ✅ `9876543210` ❌ `invalid-phone`

### GSTIN Validation
- **Pattern**: `^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$`
- **Examples**: ✅ `27AAPFU0939F1ZV` ❌ `invalid-gstin`

### Date Validation
- **Birth Date**: Must be in the past or today
- **Subscription Dates**: Start date must be before or equal to end date

### State Validation
- **Minimum Length**: 2 characters
- **Examples**: ✅ `Maharashtra` ✅ `UP` ❌ `X`

## Security Features

### Row Level Security (RLS)

The schema implements comprehensive RLS policies:

#### Roles Table
1. **View Policy**: Authenticated users can view active roles
2. **Admin Policy**: Regional managers and admins can manage roles

#### Profiles Table
1. **View Policy**: Users can only view their own profile
2. **Insert Policy**: Users can only create their own profile
3. **Update Policy**: Users can only update their own profile
4. **Delete Policy**: Users can only soft-delete their own profile

### Authentication Integration

- Uses Firebase Auth UID as the primary identifier
- Supports multiple auth providers (email, Google, phone, Apple)
- Integrates with Supabase auth for RLS enforcement

## Performance Optimizations

### Indexes

#### Roles Table
- `role_code` (programmatic lookups)
- `is_active` (active roles only)
- `hierarchy_level` (organizational queries)

#### Profiles Table
- `firebase_uid` (primary lookup)
- `email` (email-based queries)
- `role_id` (role-based queries)
- `subscription_plan` + `subscription_status` (subscription queries)
- `created_at` and `updated_at` (timestamp queries)
- `company_name` and `state_of_residence` (business queries)
- Partial index for active profiles (where `deleted_at IS NULL`)

### Automatic Triggers

- `updated_at` timestamp automatically updated on any profile or role change
- Soft delete support for data retention compliance

### Helper Functions

#### Role Management
- `get_profile_with_role()` - Get profile with role information
- `get_users_by_role()` - Find users by role code
- `user_has_permission()` - Check user permissions

#### Data Management
- `get_profile_by_firebase_uid()` - Get profile by Firebase UID
- `soft_delete_profile()` - Soft delete user profile
- `clean_phone_number()` - Validate and format phone numbers

#### Migration Support
- `migrate_role_data()` - Migrate existing role text to normalized structure
- `validate_existing_data()` - Check data quality before applying constraints

## Usage Examples

### Creating a Profile with Role

```sql
-- First, get the role ID
SELECT id FROM public.roles WHERE role_code = 'AGENT';

-- Then create the profile
INSERT INTO public.profiles (
    firebase_uid,
    email,
    display_name,
    phone_number,
    company_name,
    role_id
) VALUES (
    'firebase_user_uid_here',
    '<EMAIL>',
    'John Doe',
    '+91 9876543210',
    'ABC Insurance',
    (SELECT id FROM public.roles WHERE role_code = 'AGENT')
);
```

### Getting Profile with Role Information

```sql
SELECT * FROM public.get_profile_with_role('firebase_user_uid_here');
```

### Checking User Permissions

```sql
SELECT public.user_has_permission('firebase_user_uid_here', 'can_create_quotes');
```

### Updating Profile Preferences

```sql
UPDATE public.profiles 
SET 
    push_notifications_enabled = false,
    dark_mode_enabled = true,
    updated_at = NOW()
WHERE firebase_uid = 'firebase_user_uid_here';
```

### Querying User Profile

```sql
SELECT * FROM public.profiles 
WHERE firebase_uid = 'firebase_user_uid_here' 
AND deleted_at IS NULL;
```

### Soft Delete Profile

```sql
UPDATE public.profiles 
SET deleted_at = NOW() 
WHERE firebase_uid = 'firebase_user_uid_here';
```

## Integration with Flutter App

### Enhanced Profile Model

The existing `AppUser` model should be extended to include role information and validation:

```dart
class UserRole {
  final String id;
  final String roleName;
  final String roleCode;
  final String? description;
  final Map<String, dynamic> permissions;
  final int hierarchyLevel;
  final bool isActive;

  const UserRole({
    required this.id,
    required this.roleName,
    required this.roleCode,
    this.description,
    required this.permissions,
    required this.hierarchyLevel,
    required this.isActive,
  });

  bool hasPermission(String permission) {
    return permissions[permission] == true;
  }
}

class AppUser extends Equatable {
  // Existing fields...
  final String? companyName;
  final String? designation;
  final UserRole? role; // Changed from String to UserRole object
  final String? gstin;
  final String? stateOfResidence;
  final String? nativeLanguage;
  final DateTime? dateOfBirth;
  final String? companyLogoUrl;
  
  // App preferences
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool locationServicesEnabled;
  final bool analyticsEnabled;
  final bool darkModeEnabled;
  final bool biometricEnabled;
  final bool autoDownloadEnabled;
  final bool profileCompletionDontAskAgain;
  
  // Subscription info
  final String? subscriptionPlan;
  final String? subscriptionStatus;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionEndDate;
  final String? billingPeriod;
  
  // System fields
  final DateTime? deletedAt;
  // ... rest of the model
}
```

### Enhanced Supabase Service Integration

Create a comprehensive `ProfileService` with role management and validation:

```dart
class ProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get profile with role information
  Future<AppUser?> getProfileWithRole(String firebaseUid) async {
    final response = await _supabase
        .rpc('get_profile_with_role', params: {'uid': firebaseUid});

    return response != null ? AppUser.fromSupabaseWithRole(response) : null;
  }

  // Get available roles
  Future<List<UserRole>> getAvailableRoles() async {
    final response = await _supabase
        .from('roles')
        .select()
        .eq('is_active', true)
        .order('hierarchy_level');

    return response.map((role) => UserRole.fromSupabase(role)).toList();
  }

  // Check user permission
  Future<bool> hasPermission(String firebaseUid, String permission) async {
    final response = await _supabase
        .rpc('user_has_permission', params: {
          'uid': firebaseUid,
          'permission_key': permission
        });

    return response == true;
  }

  // Create profile with validation
  Future<void> createProfile(AppUser user) async {
    final profileData = user.toSupabase();

    // Clean phone number before saving
    if (profileData['phone_number'] != null) {
      final cleanedPhone = await _supabase
          .rpc('clean_phone_number', params: {'phone': profileData['phone_number']});
      profileData['phone_number'] = cleanedPhone;
    }

    await _supabase.from('profiles').insert(profileData);
  }

  // Update profile with validation
  Future<void> updateProfile(String firebaseUid, Map<String, dynamic> updates) async {
    // Clean phone number if provided
    if (updates['phone_number'] != null) {
      final cleanedPhone = await _supabase
          .rpc('clean_phone_number', params: {'phone': updates['phone_number']});
      updates['phone_number'] = cleanedPhone;
    }

    await _supabase
        .from('profiles')
        .update(updates)
        .eq('firebase_uid', firebaseUid);
  }

  // Get users by role
  Future<List<AppUser>> getUsersByRole(String roleCode) async {
    final response = await _supabase
        .rpc('get_users_by_role', params: {'role_code_param': roleCode});

    return response.map((user) => AppUser.fromSupabase(user)).toList();
  }

  // Validate existing data (for migration)
  Future<List<Map<String, dynamic>>> validateExistingData() async {
    final response = await _supabase.rpc('validate_existing_data');
    return List<Map<String, dynamic>>.from(response);
  }
}
```

## Migration Strategy

### Phase 1: Schema Setup
1. **Execute SQL Schema**: Run the enhanced schema in Supabase
2. **Validate Structure**: Ensure all tables, indexes, and policies are created
3. **Test RLS**: Verify security policies work correctly

### Phase 2: Data Validation & Migration
1. **Validate Existing Data**: Run `validate_existing_data()` function
2. **Clean Invalid Data**: Fix emails, phone numbers, GSTIN formats
3. **Migrate Role Data**: Use `migrate_role_data()` function
4. **Test Constraints**: Ensure all validation rules work

### Phase 3: Flutter Integration
1. **Update Models**: Implement enhanced `AppUser` and `UserRole` classes
2. **Create Services**: Implement `ProfileService` with role management
3. **Update Providers**: Modify Riverpod providers for new data structure
4. **Test Integration**: Verify CRUD operations work correctly

### Phase 4: UI Updates
1. **Role Selection**: Update profile forms to use role dropdown
2. **Permission Checks**: Implement role-based UI features
3. **Validation Feedback**: Show validation errors in forms
4. **Testing**: Comprehensive testing of all profile features

### Phase 5: Data Migration & Cleanup
1. **Migrate User Data**: Transfer existing Firebase/SharedPreferences data
2. **Verify Migration**: Ensure all data transferred correctly
3. **Remove Old Code**: Clean up old data storage mechanisms
4. **Performance Testing**: Verify performance with real data

### Migration Commands

```sql
-- Step 1: Validate existing data
SELECT * FROM public.validate_existing_data();

-- Step 2: Clean invalid data (example)
UPDATE public.profiles
SET email = NULL
WHERE email !~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';

-- Step 3: Migrate role data
SELECT public.migrate_role_data();

-- Step 4: Verify migration
SELECT COUNT(*) FROM public.profiles WHERE role_id IS NOT NULL;
```

## Benefits of Enhanced Schema

### 🎯 **Scalable Role Management**
- ✅ Consistent role naming and management
- ✅ Rich role metadata with permissions
- ✅ Organizational hierarchy support
- ✅ Easy addition of new roles without code changes
- ✅ Role-based access control foundation

### 🛡️ **Data Integrity & Quality**
- ✅ SQL-level validation prevents bad data
- ✅ Consistent email and phone formats
- ✅ Valid Indian GSTIN format enforcement
- ✅ Business rule validation (dates, relationships)
- ✅ Automatic data cleaning functions

### 🚀 **Performance & Scalability**
- ✅ Optimized indexes for common queries
- ✅ Efficient role-based lookups
- ✅ Normalized structure reduces data redundancy
- ✅ Prepared functions for complex operations

### 🔒 **Security & Compliance**
- ✅ Comprehensive RLS policies
- ✅ Role-based permission system
- ✅ Audit trail with timestamps
- ✅ Soft delete for data retention compliance

## Next Steps

1. **Execute Enhanced Schema**: Run the updated SQL in Supabase SQL editor
2. **Validate Data Quality**: Use validation functions to check existing data
3. **Test Role System**: Verify role management and permissions work correctly
4. **Update Flutter Models**: Implement enhanced AppUser and UserRole classes
5. **Migrate Existing Data**: Use migration functions to transfer current data
6. **Update UI Components**: Implement role-based features and validation feedback
7. **Performance Testing**: Verify performance with realistic data volumes

## Support & Troubleshooting

### Common Issues

**Invalid Data During Migration**
- Use `validate_existing_data()` to identify issues
- Clean data before applying constraints
- Use `clean_phone_number()` for phone validation

**Role Assignment Problems**
- Verify roles exist in roles table
- Use `migrate_role_data()` for bulk migration
- Check role_id foreign key constraints

**Permission Errors**
- Verify RLS policies are enabled
- Check Firebase UID matches auth.uid()
- Ensure user has proper role assignments

The enhanced schema provides a robust foundation for scalable user profile management with enterprise-grade data quality and security features!

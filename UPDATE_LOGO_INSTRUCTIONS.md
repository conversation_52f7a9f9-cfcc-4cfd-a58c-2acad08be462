# App Logo Update Instructions

## Current Issue
The app logo in `assets/logo/app-logo.png` currently has a blue background with white content. This needs to be updated to use the app's primary red color (#E92933) instead of blue.

## Required Changes

### 1. Update App Logo Asset
- **File Location**: `assets/logo/app-logo.png`
- **Current Color**: Blue background (#086788 or similar blue)
- **New Color**: Red background (#E92933)
- **Content**: Keep the white content/icon unchanged, only change the background color

### 2. Design Specifications
- **Background Color**: #E92933 (Primary Red)
- **Content Color**: White (#FFFFFF)
- **Format**: PNG with transparency support
- **Recommended Size**: 512x512 pixels or higher for scalability
- **Style**: Maintain current design, only change background color

### 3. Tools for Editing
You can use any of these tools to update the logo:
- **Adobe Photoshop**: Open PNG, select background layer, change color
- **GIMP** (Free): Open PNG, use bucket fill tool to change background color
- **Figma** (Free): Import PNG, change background color, export as PNG
- **Canva** (Free): Upload logo, change background color, download as PNG
- **Online Tools**: Use color replacement tools like Photopea.com

### 4. Step-by-Step Process
1. Open `assets/logo/app-logo.png` in your preferred image editor
2. Select the blue background area
3. Change the color to #E92933 (RGB: 233, 41, 51)
4. Ensure white content remains unchanged
5. Save as PNG with transparency
6. Replace the existing file in `assets/logo/app-logo.png`

### 5. Verification
After updating the logo:
1. Run `flutter clean`
2. Run `flutter pub get`
3. Test the app to see the updated logo in:
   - Splash screen
   - PDF watermarks
   - Any other logo usage throughout the app

### 6. Alternative Approach
If you prefer to create a new logo from scratch:
- Create a 512x512 pixel canvas
- Set background color to #E92933
- Add your white logo/icon content
- Save as PNG
- Replace `assets/logo/app-logo.png`

## Impact
This change will automatically update the logo appearance in:
- PDF watermarks
- Splash screen
- App icon (if using the same asset)
- Any other components that reference this logo file

## Note
The PDF generation service has been updated to properly layer the watermark on top of all content, so the updated red logo will be clearly visible in all PDF sections.

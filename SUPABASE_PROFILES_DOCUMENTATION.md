# Supabase Profiles Table Documentation

## Overview

This document describes the comprehensive profiles table schema for the All About Insurance app, designed to store all user profile data in Supabase while maintaining security and performance.

## Table Structure

### Primary Fields

| Field | Type | Description | Source |
|-------|------|-------------|---------|
| `id` | UUID | Primary key (auto-generated) | System |
| `firebase_uid` | TEXT | Firebase Auth UID (unique identifier) | Firebase Auth |
| `email` | TEXT | User email address | Firebase Auth / User Input |
| `is_email_verified` | BOOLEAN | Email verification status | Firebase Auth |
| `auth_provider` | TEXT | Authentication method used | Firebase Auth |

### Personal Information

| Field | Type | Description | Source |
|-------|------|-------------|---------|
| `display_name` | TEXT | User's full name | Firebase Auth / Profile Form |
| `phone_number` | TEXT | User's phone number | Firebase Auth / Profile Form |
| `date_of_birth` | DATE | User's date of birth | Profile Form |
| `photo_url` | TEXT | Profile picture URL | Cloud Storage |
| `company_logo_url` | TEXT | Company logo URL | Cloud Storage |

### Professional Information

| Field | Type | Description | Source |
|-------|------|-------------|---------|
| `company_name` | TEXT | Company/Organization name | Profile Form |
| `designation` | TEXT | Job title/position | Profile Form |
| `role` | TEXT | Professional role (e.g., Insurance Agent) | Profile Form |
| `gstin` | TEXT | GST Identification Number | Profile Form |
| `state_of_residence` | TEXT | State where user resides | Profile Form |
| `native_language` | TEXT | Preferred language | Profile Form |

### App Preferences

| Field | Type | Default | Description |
|-------|------|---------|-------------|
| `push_notifications_enabled` | BOOLEAN | TRUE | Push notification preference |
| `email_notifications_enabled` | BOOLEAN | FALSE | Email notification preference |
| `location_services_enabled` | BOOLEAN | TRUE | Location access preference |
| `analytics_enabled` | BOOLEAN | FALSE | Analytics sharing preference |
| `dark_mode_enabled` | BOOLEAN | FALSE | Dark mode preference |
| `biometric_enabled` | BOOLEAN | TRUE | Biometric authentication preference |
| `auto_download_enabled` | BOOLEAN | FALSE | Auto-download preference |
| `profile_completion_dont_ask_again` | BOOLEAN | FALSE | Skip profile completion prompts |

### Subscription Information

| Field | Type | Description |
|-------|------|-------------|
| `subscription_plan` | TEXT | Plan type: basic, pro, enterprise |
| `subscription_status` | TEXT | Status: active, inactive, cancelled, expired |
| `subscription_start_date` | TIMESTAMPTZ | Subscription start date |
| `subscription_end_date` | TIMESTAMPTZ | Subscription end date |
| `billing_period` | TEXT | Billing cycle: monthly, annual |

### System Fields

| Field | Type | Description |
|-------|------|-------------|
| `created_at` | TIMESTAMPTZ | Profile creation timestamp |
| `updated_at` | TIMESTAMPTZ | Last update timestamp (auto-updated) |
| `last_sign_in_at` | TIMESTAMPTZ | Last sign-in timestamp |
| `deleted_at` | TIMESTAMPTZ | Soft delete timestamp (NULL = active) |

## Security Features

### Row Level Security (RLS)

The table implements comprehensive RLS policies:

1. **View Policy**: Users can only view their own profile
2. **Insert Policy**: Users can only create their own profile
3. **Update Policy**: Users can only update their own profile
4. **Delete Policy**: Users can only soft-delete their own profile

### Authentication Integration

- Uses Firebase Auth UID as the primary identifier
- Supports multiple auth providers (email, Google, phone, Apple)
- Integrates with Supabase auth for RLS enforcement

## Performance Optimizations

### Indexes

- `firebase_uid` (primary lookup)
- `email` (email-based queries)
- `subscription_plan` + `subscription_status` (subscription queries)
- `created_at` and `updated_at` (timestamp queries)
- Partial index for active profiles (where `deleted_at IS NULL`)

### Automatic Triggers

- `updated_at` timestamp automatically updated on any profile change
- Soft delete support for data retention compliance

## Usage Examples

### Creating a Profile

```sql
INSERT INTO public.profiles (
    firebase_uid,
    email,
    display_name,
    phone_number,
    company_name,
    role
) VALUES (
    'firebase_user_uid_here',
    '<EMAIL>',
    'John Doe',
    '+91 **********',
    'ABC Insurance',
    'Insurance Agent'
);
```

### Updating Profile Preferences

```sql
UPDATE public.profiles 
SET 
    push_notifications_enabled = false,
    dark_mode_enabled = true,
    updated_at = NOW()
WHERE firebase_uid = 'firebase_user_uid_here';
```

### Querying User Profile

```sql
SELECT * FROM public.profiles 
WHERE firebase_uid = 'firebase_user_uid_here' 
AND deleted_at IS NULL;
```

### Soft Delete Profile

```sql
UPDATE public.profiles 
SET deleted_at = NOW() 
WHERE firebase_uid = 'firebase_user_uid_here';
```

## Integration with Flutter App

### Profile Model Updates

The existing `AppUser` model should be extended to include additional fields:

```dart
class AppUser extends Equatable {
  // Existing fields...
  final String? companyName;
  final String? designation;
  final String? role;
  final String? gstin;
  final String? stateOfResidence;
  final String? nativeLanguage;
  final DateTime? dateOfBirth;
  final String? companyLogoUrl;
  
  // App preferences
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool locationServicesEnabled;
  final bool analyticsEnabled;
  final bool darkModeEnabled;
  final bool biometricEnabled;
  final bool autoDownloadEnabled;
  final bool profileCompletionDontAskAgain;
  
  // Subscription info
  final String? subscriptionPlan;
  final String? subscriptionStatus;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionEndDate;
  final String? billingPeriod;
  
  // System fields
  final DateTime? deletedAt;
  // ... rest of the model
}
```

### Supabase Service Integration

Create a `ProfileService` to handle all profile operations:

```dart
class ProfileService {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  Future<AppUser?> getProfile(String firebaseUid) async {
    final response = await _supabase
        .from('profiles')
        .select()
        .eq('firebase_uid', firebaseUid)
        .is_('deleted_at', null)
        .maybeSingle();
    
    return response != null ? AppUser.fromSupabase(response) : null;
  }
  
  Future<void> createProfile(AppUser user) async {
    await _supabase.from('profiles').insert(user.toSupabase());
  }
  
  Future<void> updateProfile(String firebaseUid, Map<String, dynamic> updates) async {
    await _supabase
        .from('profiles')
        .update(updates)
        .eq('firebase_uid', firebaseUid);
  }
}
```

## Migration Strategy

1. **Phase 1**: Create the table structure in Supabase
2. **Phase 2**: Update Flutter models and services
3. **Phase 3**: Migrate existing user data from Firebase/SharedPreferences
4. **Phase 4**: Update UI components to use Supabase data
5. **Phase 5**: Remove old data storage mechanisms

## Next Steps

1. Execute the SQL schema in Supabase SQL editor
2. Test RLS policies with different user scenarios
3. Update Flutter app models and services
4. Implement data migration from existing storage
5. Update UI components to reflect new data structure
